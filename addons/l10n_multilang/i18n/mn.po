# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_multilang
# 
# Translators:
# Baskhu<PERSON> <baskhuu<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# hish, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# tserend<PERSON>a tsogtoo <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2024
# Nomin <PERSON> <<EMAIL>>, 2024
# <PERSON>x, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-14 15:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Батболд <<EMAIL>>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account
msgid "Account"
msgstr "Данс"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_chart_template
msgid "Account Chart Template"
msgstr "Дансны модны загвар"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group
msgid "Account Group"
msgstr "Санхүүгийн бүлэг"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account__name
msgid "Account Name"
msgstr "Дансны нэр"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_tag
msgid "Account Tag"
msgstr "Дансны пайз"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_report_line
msgid "Account Tax Report Line"
msgstr "Татварын тайлан"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_res_country_state__name
msgid ""
"Administrative divisions of a country. E.g. Fed. State, Departement, Canton"
msgstr "Улсын засаг захиргааны нэгж. Тухайлбал: Холбоо. Муж, Хэсэг, Аймаг"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_analytic_account
#: model:ir.model.fields,field_description:l10n_multilang.field_account_analytic_account__name
msgid "Analytic Account"
msgstr "Шинжилгээний данс"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_tax_report_line__name
msgid "Complete name for this report line, to be used in report."
msgstr "Тайлангийн мөрийн нэр."

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_res_country_state
msgid "Country state"
msgstr "Муж"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Нэхэмжлэл дээр харуулах"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__name
msgid "Fiscal Position"
msgstr "Татварын тайлангийн тохируулга"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__name
msgid "Fiscal Position Template"
msgstr "Татварын тайлангийн тохируулгын загвар"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_base_language_install
msgid "Install Language"
msgstr "Хэл суулгах"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_journal__name
msgid "Journal Name"
msgstr "Журналын нэр"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__description
msgid "Label on Invoices"
msgstr "Нэхэмжлэл дээрх тэмдэглэгээ"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Нэхэмжлэл дээр хэвлэгдэх ёстой хуулийн нэршил."

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_report_line__name
msgid "Name"
msgstr "Нэр"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__note
msgid "Notes"
msgstr "Тэмдэглэлүүд"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_tax_report_line__tag_name
msgid ""
"Short name for the tax grid corresponding to this report line. Leave empty "
"if this report line should not correspond to any such grid."
msgstr ""

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__spoken_languages
msgid "Spoken Languages"
msgstr "Ярианы хэлүүд"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_res_country_state__name
msgid "State Name"
msgstr "Мужын нэр"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_chart_template__spoken_languages
msgid ""
"State here the languages for which the translations of templates could be "
"loaded at the time of installation of this localization module and copied in"
" the final object when generating them from templates. You must provide the "
"language codes separated by ';'"
msgstr ""
"Энэ орон нутагийн модулийг суулгах үедорчуулгын үлгэрүүдийн ачаалж улмаар "
"эцсийн обьектуудыг үүсгэхдээ үлгэрүүдээс хуулах хэлийг энд "
"дурьдана.Хэлүүдийн кодыг ';'-с тусгаарлан бичнэ."

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_tag__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_report_line__tag_name
msgid "Tag Name"
msgstr "Пайзын нэр"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax
msgid "Tax"
msgstr "Татвар"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__name
msgid "Tax Name"
msgstr "Татварын нэр"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group_template
msgid "Template for Account Groups"
msgstr ""

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Татварын тайлангийн тохируулгын загвар"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_template
msgid "Templates for Accounts"
msgstr "Дансны загварууд"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Татварын загварууд"
