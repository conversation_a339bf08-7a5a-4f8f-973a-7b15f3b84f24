# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_forum
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:06+0000\n"
"PO-Revision-Date: 2018-08-24 09:34+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:24
#, python-format
msgid ""
" karma is required to perform this action. You can earn karma by having your"
" answers upvoted by the community."
msgstr ""

#. module: website_forum
#: model:mail.template,subject:website_forum.validation_email
msgid "${object.company_id.name} Forums validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid ""
"&amp;nbsp;&amp;nbsp;<i class=\"fa fa-times\" title=\"Close\" role=\"img\" "
"aria-label=\"Close\"/>&amp;nbsp;&amp;nbsp;"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid ""
"(Un)archiving a forum automatically (un)archives its posts. Do you want to "
"proceed?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid ""
"(Un)archiving a post automatically (un)archives its answers. Do you want to "
"proceed?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "45% of questions shared"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:30
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Closed]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Deleted]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Offensive]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.users
msgid "<b> badges:</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead\n"
"    either edit the question or add a comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead\n"
"    add a comment on the other answers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not start debates</b>\n"
"    This community Q&amp;A is not a discussion group. Please avoid holding debates in\n"
"    your answers as they tend to dilute the essence of questions and answers. For\n"
"    brief discussions please use commenting facility."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other questions</b>.\n"
"    Instead add a comment indicating <i>\"Possible duplicate\n"
"    of...\"</i>. However, it's fine to include links to other\n"
"    questions or answers providing relevant additional\n"
"    information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just provide a\n"
"    link a solution</b>. Instead provide the solution\n"
"    description text in your answer, even if it's just a\n"
"    copy/paste. Links are welcome, but should be complementary to\n"
"    answer, referring sources or additional reading."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> You can\n"
"    search questions by their title or tags.  It’s also OK to\n"
"    answer your own question."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<b>No badge yet!</b><br/>"
msgstr "<b>មិនទាន់មាន badge នៅឡើយ!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<b>No vote given by you yet!</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Please avoid asking questions that are too subjective\n"
"    and argumentative</b> or not relevant to this community."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"        <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"        - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"        - it really helps to select the best questions and answers!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "<b>Share</b> Something Awesome."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "<i class=\"fa fa-arrow-right\"/> Back to"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "<i class=\"fa fa-arrow-right\"/> Read Guidelines"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<i class=\"fa fa-arrow-right\"/>Edit Your Profile"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<i class=\"text-muted\">awarded users</i>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<small class=\"text-muted\">profile</small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "<span class=\"sr-only\">Select Post</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span class=\"text-muted\">(only one answer per question is allowed)</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">bio</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted\">or</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">stats</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span> on </span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span> • </span>\n"
"                Flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>By </span>"
msgstr "<span>ដោយ</span>"

#. module: website_forum
#: model:mail.template,body_html:website_forum.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 10px;\">Your Forum</span><br/>\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            ${object.company_id.name} Forum validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\">\n"
"                        <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello ${object.name},<br/><br/>\n"
"                            You have been invited to validate your email in order to get access to \"${object.company_id.name}\" Q/A Forums.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a href=\"${ctx.get('token_url')}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         ${user.company_id.name}\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        ${user.company_id.phone}\n"
"                        % if user.company_id.email:\n"
"                            | <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">\n"
"                                ${user.company_id.email}\n"
"                            </a>\n"
"                        % endif\n"
"                        % if user.company_id.website:\n"
"                            | <a href=\"${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                                ${user.company_id.website}\n"
"                            </a>\n"
"                        % endif\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid ""
"A tag is a label that categorizes your question with other,\n"
"            similar questions. Using the right tags makes it easier for\n"
"            others to find and answer your question. (Hover the mouse to follow/unfollow tag(s))"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "About This Community"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Accept <i class=\"fa fa-check\"/>"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
msgid "Active"
msgstr "សកម្ម"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Activity"
msgstr "សកម្មភាព"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Add a Comment"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:45
#, python-format
msgid "Add to menu"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:203
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "ទាំងអស់"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Answer"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:521
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__category
msgid "Appears in"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "ឯកសារ"

#. module: website_forum
#: selection:forum.post,post_type:0
msgid "Article"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask Your Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Ask a Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:29
#, python-format
msgid "Ask the question in this forum by clicking on the button."
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Asked:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "អ្នកបង្កើត"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
#: model_terms:ir.ui.view,arch_db:website_forum.forum_user_tooltip
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.users
msgid "Avatar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Avoid unnecessary introductions (Hi,... Please... Thanks...),"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:343
#: code:addons/website_forum/controllers/main.py:345
#: code:addons/website_forum/controllers/main.py:414
#, python-format
msgid "Bad Request"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__badge_ids
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Badges"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"            you receive badges for being especially helpful. Badges\n"
"            appear on your profile page, and your posts."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Biography"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Bronze badge"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__bronze_badge
msgid "Bronze badges count"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Check available badges"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "City"
msgstr "ក្រុង"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Clear"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:24
#, python-format
msgid "Click <em>Continue</em> to create the forum."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:73
#, python-format
msgid "Click here to accept this answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate to the "
"forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get bad question samples"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get good question titles"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:65
#, python-format
msgid "Click to post your answer."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:51
#, python-format
msgid "Click to post your question."
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Close"
msgstr "បិទ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Close Post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Close post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Comment"
msgstr "មតិយោបល់"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.challenge.line,name:website_forum.line_chief_commentator
#: model:gamification.challenge.line,name:website_forum.line_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "Comments"
msgstr ""

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_1
#: model:gamification.challenge.line,name:website_forum.line_configure_profile
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
msgid "Completed own biography"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Congratulations! Your email has just been validated. You may now participate"
" to our forums."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "មាតិកា"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as an answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country"
msgstr "ប្រទេស"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country..."
msgstr "ប្រទេស ..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr ""

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr ""

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.challenge.line,name:website_forum.line_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default Order"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_post_type
msgid "Default Post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__category
msgid "Define the visibility of the challenge through menus"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Delete"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
msgid "Description"
msgstr "ការពិពណ៌​នា​"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Design Welcome Message"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.challenge.line,name:website_forum.line_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Discussion"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_discussion
msgid "Discussions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Edit"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Edit Profile"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Edit Your Previous Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Edit your Link"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.challenge.line,name:website_forum.line_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Email"
msgstr "អុីម៉ែល"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.challenge.line,name:website_forum.line_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:19
#, python-format
msgid "Enter a name for your new forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "External link"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite Count"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_1
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_stellar_question_25
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_5
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Favourite Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Filter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Filter on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Flag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Followed"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Followed Questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_channel_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_channel_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"For example, if you ask an interesting question or give a helpful answer, your\n"
"    input will be upvoted. On the other hand if the answer is misleading - it will\n"
"    be downvoted. Each vote in favor will generate 10 points, each vote against\n"
"    will subtract 10 points. There is a limit of 200 points that can be accumulated\n"
"    for a question or answer per day. The table given at the end explains reputation point\n"
"    requirements for each type of moderation task."
msgstr ""

#. module: website_forum
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#: model:website.menu,name:website_forum.menu_website_forums
msgid "Forum"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge__level
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge_user__level
msgid "Forum Badge Level"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.res_users_view_form_preference
#: model_terms:ir.ui.view,arch_db:website_forum.view_users_form_forum
msgid "Forum Karma"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:34
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#, python-format
msgid "Forum Name"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Forum Settings"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge
msgid "Gamification Badge"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:33
#, python-format
msgid "Give your question title."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Gold badge"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__gold_badge
msgid "Gold badges count"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_answer
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_answer
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
msgid "Guidelines"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_guru
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr ""

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"How to create a physical inventory at an anterior date?, How is the "
"'remaining hours' field computed on tasks?, How to configure TPS and TVQ's "
"canadian taxes?"
msgstr ""

#. module: website_forum
#: selection:gamification.challenge,category:0
msgid "Human Resources / Engagement"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If this approach is not for you, please respect the community and use Google+\n"
"    communities instead."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If you fit in one of these example or if your motivation for asking the\n"
"    question is “I would like to participate in a discussion about ______”, then\n"
"    you should not be asking here but on our mailing lists.\n"
"    However, if your motivation is “I would like others to explain ______ to me”,\n"
"    then you are probably OK."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:43
#, python-format
msgid "Insert tags related to your question."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Inventory Date Problem, Task remaining hours, Can you help solve solve my "
"tax computation problem in Canada?"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "It appears your email has not been verified."
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:890
#: code:addons/website_forum/models/forum.py:910
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__karma
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Karma"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Keep Informed"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Last Updated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Last updated:"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_post_type:0
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Link"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_link
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Links"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "សារ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation Tools"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "More over:"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Most Voted"
msgstr "ការបោះឆ្នោតច្រើនជាងគេ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:47
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Name"
msgstr "ឈ្មោះ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr ""

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Discussion"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:33
#, python-format
msgid "New Forum"
msgstr ""

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "New Topic"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Newest"
msgstr "ថ្មីបំផុត"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_answer
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum post yet."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:511
#, python-format
msgid "Not enough karma to retag."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
msgid "Number of Views"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
msgid "Number of answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Offensive Post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "On"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "On average,"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Orders"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "Our forums"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.challenge.line,name:website_forum.line_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Pending"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "People"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Please enter a descriptive question (should finish by a '?')"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_popular_question
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_notable_question
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_famous_question
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "ប៉ុស្ត"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Post Title"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Post Types"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Post Your Topic"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:448
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Provide enough details and, if possible, give an example."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Public profile"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.challenge.line,name:website_forum.line_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:59
#, python-format
msgid "Put your answer here."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:37
#, python-format
msgid "Put your question here."
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Question"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:524
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Question has accepted answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:345
#, python-format
msgid "Question should not be empty."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Question tools"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Questions"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:542
#, python-format
msgid "Re: %s"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reactivate"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Real name"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Redirect to external link"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reject <i class=\"fa fa-times\"/>"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Relevance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reopen"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reply"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the question list."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.challenge.line,name:website_forum.line_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Seen:"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:15
#, python-format
msgid "Select this menu item to create a new forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.challenge.line,name:website_forum.line_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Accept answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Set a clear, explicit and concise question title\n"
"                (check"
msgstr ""

#. module: website_forum
#: selection:gamification.challenge,category:0
msgid "Settings / Gamification Tools"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"Share an awesome link. Your post will appear in the 'Newest' top-menu.\n"
"            If the community vote on your post, it will get traction by being promoted\n"
"            in the homepage."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:39
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Silver badge"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__silver_badge
msgid "Silver badges count"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:26
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:101
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:150
#, python-format
msgid "Sorry you must be logged to vote"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:200
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:145
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sort by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Stats"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Submit a Link"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.challenge.line,name:website_forum.line_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "ធែក"

#. module: website_forum
#: sql_constraint:forum.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Tags"
msgstr "ធែក"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.challenge.line,name:website_forum.line_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.challenge.line,name:website_forum.line_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:6
#, python-format
msgid "Thanks for posting!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The"
msgstr "*"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The flagged queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"The goal of this site is create a relevant knowledge base that would answer\n"
"    questions related to Odoo."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The offensive queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The validation queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced users of\n"
"    this site in order to improve the overall quality of the knowledge base content.\n"
"    Such privileges are granted based on user karma level: you will be able to do the same\n"
"    once your karma gets high enough."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"This community is for professional and enthusiast users,\n"
"    partners and programmers. You can ask questions about:"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:51
#: model:forum.forum,description:website_forum.forum_help
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
#, python-format
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:411
#, python-format
msgid "This forum does not allow %s"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:111
#, python-format
msgid "This post can not be flagged"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:106
#, python-format
msgid "This post is already flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and not yet published... Do you "
"want <i>Accept</i> or <i>Reject</i> this post ?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "This profile is private!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Threads"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
msgid "Title"
msgstr "ចំណងជើង​"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:343
#: code:addons/website_forum/controllers/main.py:414
#, python-format
msgid "Title should not be empty."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"To prevent your question from being flagged and possibly removed, avoid asking\n"
"    subjective questions where …"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Trending"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__post_type
msgid "Type"
msgstr "ប្រភេទ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content_link
msgid "URL"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__content_link
msgid "URL of Link Articles"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "URL to Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread
msgid "Unread Messages"
msgstr "សារមិនទាន់អាន"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Update"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Update on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_student
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_question
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_question
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr ""

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_question
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_question
msgid ""
"Users can answer only once per question. Contributors can edit answers and "
"mark the right ones."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Validate"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "View Your Badges"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "ទស្សនីយភាព"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Votes"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
msgid "Waiting Validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "កំពុងរង់ចាំការផ្ទៀងផ្ទាត់"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"We keep a high level of quality in showcased posts, around 20% of the submited\n"
"            posts will be featured."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Website"
msgstr "វែបសាយ"

#. module: website_forum
#: selection:gamification.challenge,category:0
msgid "Website / Forum"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "សារវែបសាយ"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "ប្រវត្តិទំនាក់ទំនងវែបសាយ"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What kinds of questions can I ask here?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my answers?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my questions?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"When a question or answer is upvoted, the user who posted them will gain some\n"
"    points, which are called \"karma points\". These points serve as a rough\n"
"    measure of the community trust to him/her. Various moderation tasks are\n"
"    gradually assigned to the users based on those points."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_link
msgid "When clicking on the post, it redirects to an external link"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "Why can other people edit my questions/answers?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"You already have one pending post.<br/>\n"
"            Please wait for a moderator to validate your previous post before continuing."
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:139
#, python-format
msgid ""
"You cannot choose %s as default post since the forum does not allow it."
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:438
#, python-format
msgid "You cannot create recursive forum posts."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:953
#, python-format
msgid "You don't have enough karma to create a new Tag."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"You should only ask practical, answerable questions based\n"
"    on actual problems that you face. Chatty, open-ended\n"
"    questions diminish the usefulness of this site and push\n"
"    other questions off the front page."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Your Discussion Title..."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Your Question Title..."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "accept any answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "and"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "back to post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "bad examples"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "bronze"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by activity date"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most voted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by newest"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by relevance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "close any posts"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "contains offensive or malicious remarks"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any question or answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete own comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "downvote"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "duplicate post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "e.g. https://www.odoo.com"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "edit any post, view offensive flags"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "flag offensive, close own questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "follower(s)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "gold"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "good examples"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "has been closed"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:36
#, python-format
msgid "here"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to develop modules for your own need,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to install Odoo on a specific infrastructure,"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "inappropriate and unacceptable statements"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "insert text link, upload files"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "insulting and offensive language"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "karma"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid "karma points"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "last connection"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "location"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "member since"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "not a real post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "not relevant or out dated"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "off-topic or not relevant"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "នៅ"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "racist and hate speech"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "silver"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "spam or advertising"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "specific questions about Odoo service offers, etc."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "threatening language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "time"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "times"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "too localized"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "too subjective and argumentative"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "upvote, add comments"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "views"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "violent language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "votes"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "waiting for validation"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "website"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "your biography can be seen as tooltip"
msgstr ""
