# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.addons.auth_signup.models.res_users import SignupError
from odoo.addons.web.controllers.main import ensure_db, Home
from odoo.exceptions import UserError
import logging
import werkzeug

_logger = logging.getLogger(__name__)


class AuthClientController(http.Controller):

    # ===== AUTHENTIFICATION CLIENTS =====

    @http.route('/auth/client/register', type='http', auth="public", website=True, csrf=False)
    def client_register_form(self, **post):
        """Affiche le formulaire d'inscription client"""
        if request.httprequest.method == 'POST':
            return self._process_client_registration(post)

        # GET request - Affichage du formulaire
        return request.render('authentification.client_register_form', {
            'values': {},
            'errors': {}
        })

    def _process_client_registration(self, post):
        """Traite l'inscription d'un nouveau client"""
        try:
            # Validation des données
            errors = self._validate_registration_data(post)

            if errors:
                return request.render('authentification.client_register_form', {
                    'errors': errors,
                    'values': post
                })

            # Créer le client SANS utilisateur Odoo pour simplifier
            client_vals = {
                'nom': post.get('nom'),
                'prenom': post.get('prenom'),
                'nomSociete': post.get('nomSociete', ''),
                'email': post.get('email'),
                'telephone': post.get('telephone'),
                'adresse': post.get('adresse'),
                'motDePasse': post.get('motDePasse'),
            }

            # Créer le client directement
            client = request.env['auth.client'].sudo().create(client_vals)
            _logger.info(f'✅ Client créé avec succès: {client.id} pour {post.get("email")}')

            # Rediriger vers la page de connexion avec un message de succès
            return request.render('authentification.client_login_standalone', {
                'success': f'✅ Inscription réussie ! Bienvenue {client.prenom} {client.nom}. Vous pouvez maintenant vous connecter.',
                'values': {'email': post.get('email')}
            })

        except Exception as e:
            _logger.error('Erreur lors de l\'inscription client: %s', str(e))
            return request.render('authentification.client_register_form', {
                'error': f'Une erreur est survenue lors de l\'inscription: {str(e)}',
                'values': post
            })

    def _validate_registration_data(self, post):
        """Valide les données d'inscription"""
        errors = {}

        import re

        # Validation du nom
        if not post.get('nom') or not post.get('nom').strip():
            errors['nom'] = 'Le nom est obligatoire'
        elif not re.match(r'^[a-zA-ZÀ-ÿ\s]+$', post.get('nom').strip()):
            errors['nom'] = 'Le nom ne doit contenir que des lettres'

        # Validation du prénom
        if not post.get('prenom') or not post.get('prenom').strip():
            errors['prenom'] = 'Le prénom est obligatoire'
        elif not re.match(r'^[a-zA-ZÀ-ÿ\s]+$', post.get('prenom').strip()):
            errors['prenom'] = 'Le prénom ne doit contenir que des lettres'

        # Validation de l'email
        if not post.get('email'):
            errors['email'] = 'L\'email est obligatoire'
        else:
            # Vérifier le format de l'email
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, post.get('email')):
                errors['email'] = 'Format d\'email invalide'
            else:
                # Vérifier si l'email existe déjà
                existing_client = request.env['auth.client'].sudo().search([('email', '=', post.get('email'))])
                if existing_client:
                    errors['email'] = 'Un client avec cet email existe déjà'

                existing_user = request.env['res.users'].sudo().search([('login', '=', post.get('email'))])
                if existing_user:
                    errors['email'] = 'Un utilisateur avec cet email existe déjà'

        # Validation du téléphone (8 chiffres exactement)
        if not post.get('telephone'):
            errors['telephone'] = 'Le téléphone est obligatoire'
        else:
            # Nettoyer le numéro (enlever espaces, tirets, etc.)
            clean_phone = re.sub(r'[^\d]', '', post.get('telephone'))
            if len(clean_phone) != 8 or not clean_phone.isdigit():
                errors['telephone'] = 'Le numéro de téléphone doit contenir exactement 8 chiffres'

        # Validation de l'adresse
        if not post.get('adresse') or not post.get('adresse').strip():
            errors['adresse'] = 'L\'adresse est obligatoire'
        elif len(post.get('adresse').strip()) < 10:
            errors['adresse'] = 'L\'adresse doit contenir au moins 10 caractères'

        # Validation du mot de passe
        if not post.get('motDePasse'):
            errors['motDePasse'] = 'Le mot de passe est obligatoire'
        elif len(post.get('motDePasse', '')) < 6:
            errors['motDePasse'] = 'Le mot de passe doit contenir au moins 6 caractères'

        # Validation de la confirmation du mot de passe
        if not post.get('confirmMotDePasse'):
            errors['confirmMotDePasse'] = 'La confirmation du mot de passe est obligatoire'
        elif post.get('motDePasse') != post.get('confirmMotDePasse'):
            errors['confirmMotDePasse'] = 'Les mots de passe ne correspondent pas'

        return errors

    @http.route('/auth/client/login', type='http', auth="public", website=True, csrf=False)
    def client_login_form(self, **post):
        """Affiche le formulaire de connexion client sans navbar"""
        if request.httprequest.method == 'POST':
            return self._process_client_login(post)

        # GET request - Affichage du formulaire sans navbar
        return request.render('authentification.client_login_standalone', {
            'values': {},
            'errors': {}
        })

    def _process_client_login(self, post):
        """Traite la connexion d'un client"""
        try:
            email = post.get('email')
            password = post.get('motDePasse')

            if not email or not password:
                return request.render('authentification.client_login_standalone', {
                    'error': 'Email et mot de passe sont obligatoires',
                    'values': post
                })

            # Authentifier le client
            client = request.env['auth.client'].sudo().authenticate_client(email, password)

            if not client:
                return request.render('authentification.client_login_standalone', {
                    'error': 'Email ou mot de passe incorrect',
                    'values': post
                })

            # ✅ CONNEXION SIMPLE RÉUSSIE - Stocker le client en session
            _logger.info(f'Connexion simple réussie pour {email}')

            # Stocker l'ID du client en session
            request.session['client_id'] = client.id
            request.session['client_email'] = client.email
            request.session['client_nom'] = client.nom

            return request.render('authentification.client_login_standalone', {
                'success': f'✅ Connexion réussie ! Bienvenue {client.nom}',
                'client': client,
                'show_success': True
            })

        except Exception as e:
            _logger.error('Erreur lors de la connexion client: %s', str(e))
            return request.render('authentification.client_login_standalone', {
                'error': f'Une erreur est survenue lors de la connexion: {str(e)}',
                'values': post
            })

    @http.route('/auth/client/logout', type='http', auth="public", website=True)
    def client_logout(self, **kw):
        """Déconnecte le client"""
        # Nettoyer les données client de la session
        if 'client_id' in request.session:
            del request.session['client_id']
        if 'client_email' in request.session:
            del request.session['client_email']
        if 'client_nom' in request.session:
            del request.session['client_nom']

        request.session.logout(keep_db=True)
        return request.redirect('/auth/client/login')

    @http.route('/auth/client/test/<string:email>', type='http', auth="public", website=True)
    def test_client(self, email, **kw):
        """Test rapide d'un client spécifique"""
        try:
            client = request.env['auth.client'].sudo().search([('email', '=', email)], limit=1)
            if not client:
                return f"<h1>Client non trouvé pour email: {email}</h1>"

            user_info = "AUCUN UTILISATEUR LIÉ"
            if client.user_id:
                user_info = f"Utilisateur ID: {client.user_id.id}, Login: {client.user_id.login}, Actif: {client.user_id.active}"

            return f"""
            <html>
            <head><title>Test Client {email}</title></head>
            <body style="font-family: Arial, sans-serif; margin: 20px;">
                <h1>Test Client: {email}</h1>
                <p><strong>Client ID:</strong> {client.id}</p>
                <p><strong>Nom:</strong> {client.nom}</p>
                <p><strong>Email:</strong> {client.email}</p>
                <p><strong>User ID:</strong> {client.user_id.id if client.user_id else 'MANQUANT'}</p>
                <p><strong>Utilisateur:</strong> {user_info}</p>
                <p><a href="/auth/debug/clients" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">Voir tous les clients</a></p>
                <p><a href="/auth/client/login" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">Tester la connexion</a></p>
            </body>
            </html>
            """
        except Exception as e:
            return f"<h1>Erreur: {str(e)}</h1>"

    @http.route('/auth/client/dashboard', type='http', auth="user", website=True)
    def client_dashboard(self, **kw):
        """Tableau de bord client - redirige vers la page d'accueil"""
        return request.redirect('/')

    @http.route('/auth/client/projects', type='http', auth="public", website=True)
    def client_projects(self, **kw):
        """Vue des projets pour les clients authentifiés"""
        try:
            # Vérifier si le client est connecté
            client_id = request.session.get('client_id')
            client_nom = request.session.get('client_nom', 'Client')

            if not client_id:
                return f"""
                <html>
                <head>
                    <title>Connexion Requise</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body {{ background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh; }}
                        .container {{ max-width: 500px; margin: 100px auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; }}
                        .btn {{ padding: 12px 24px; border-radius: 8px; font-weight: 500; text-decoration: none; display: inline-block; margin: 10px; }}
                        .btn-primary {{ background: #007bff; color: white; }}
                        h2 {{ color: #dc3545; margin-bottom: 20px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2><i class="fas fa-lock"></i> Connexion Requise</h2>
                        <p>Vous devez être connecté pour voir vos projets.</p>
                        <a href="/auth/client/login" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Se connecter
                        </a>
                    </div>
                </body>
                </html>
                """

            # Essayer d'utiliser le modèle auth.projet s'il existe
            try:
                # Récupérer les projets du client connecté uniquement
                projects = request.env['auth.projet'].sudo().search([
                    ('client_id', '=', client_id),
                    ('active', '=', True)
                ])

                # Récupérer les informations du client
                client = request.env['auth.client'].sudo().browse(client_id)

                # Construire la page HTML
                projects_html = ""
                if projects:
                    for project in projects:
                        # Récupérer les besoins du projet
                        besoins = request.env['auth.projet.besoin'].sudo().search([('projet_id', '=', project.id)])
                        besoins_count = len(besoins)

                        # Calculer le budget total des besoins
                        budget_besoins = sum(besoin.budget or 0 for besoin in besoins)

                        # Déterminer la couleur de priorité
                        priority_color = {{
                            'basse': '#28a745',
                            'normale': '#ffc107',
                            'urgente': '#dc3545'
                        }}.get(project.priorite, '#6c757d')

                        # Déterminer l'icône du type
                        type_icon = {{
                            'web': 'fas fa-globe',
                            'mobile': 'fas fa-mobile-alt',
                            'erp': 'fas fa-cogs',
                            'ecommerce': 'fas fa-shopping-cart',
                            'consulting': 'fas fa-handshake'
                        }}.get(project.typeProjet, 'fas fa-project-diagram')

                        # Obtenir le libellé du secteur
                        secteur_labels = {
                            'industrie': 'Industrie et Manufacturing',
                            'commerce': 'Commerce et Distribution',
                            'services': 'Services et Consulting',
                            'finance': 'Finance et Banque',
                            'immobilier': 'Immobilier et Construction',
                            'transport': 'Transport et Logistique',
                            'technologie': 'Technologie et IT',
                            'tourisme': 'Tourisme et Hôtellerie',
                            'public': 'Secteur Public',
                            'autre': 'Autre',
                        }
                        secteur_label = secteur_labels.get(project.secteurSociete, project.secteurSociete)

                        projects_html += f"""
                        <div class="project-card">
                            <div class="project-header">
                                <h4><i class="{type_icon}"></i> {project.titre}</h4>
                                <span class="priority-badge" style="background-color: {priority_color};">
                                    {project.priorite.title()}
                                </span>
                            </div>
                            <div class="project-info">
                                <p><strong>ID:</strong> {project.idProjet}</p>
                                <p><strong>Secteur:</strong> {secteur_label}</p>
                                <p><strong>Type:</strong> {project.typeProjet}</p>
                                <p><strong>Budget:</strong> {project.budget} DT</p>
                                <p><strong>Besoins:</strong> {besoins_count} besoin(s)</p>
                                {f'<p><strong>Budget besoins:</strong> {budget_besoins} DT</p>' if budget_besoins > 0 else ''}
                                <p><strong>Créé le:</strong> {project.dateCreation.strftime('%d/%m/%Y') if project.dateCreation else 'N/A'}</p>
                                {f'<p><strong>Début:</strong> {project.dateDebut.strftime("%d/%m/%Y")}</p>' if project.dateDebut else ''}
                                {f'<p><strong>Fin:</strong> {project.dateFin.strftime("%d/%m/%Y")}</p>' if project.dateFin else ''}
                            </div>
                            {f'<div class="project-description"><strong>Description:</strong> {project.description}</div>' if project.description else ''}
                        </div>
                        """
                else:
                    projects_html = """
                    <div class="no-projects">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h4>Aucun projet trouvé</h4>
                        <p>Vous n'avez pas encore créé de projet.</p>
                        <a href="/auth/client/create-project" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Créer mon premier projet
                        </a>
                    </div>
                    """

                return f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Mes Projets - {client_nom}</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                    <style>
                        body {{
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            min-height: 100vh;
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        }}
                        .container {{
                            max-width: 1200px;
                            margin: 30px auto;
                            background: white;
                            padding: 40px;
                            border-radius: 15px;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                        }}
                        .header {{
                            text-align: center;
                            margin-bottom: 40px;
                            border-bottom: 2px solid #e9ecef;
                            padding-bottom: 20px;
                        }}
                        .client-info {{
                            background: #e3f2fd;
                            padding: 15px;
                            border-radius: 8px;
                            margin-bottom: 30px;
                            border-left: 4px solid #2196f3;
                        }}
                        .project-card {{
                            background: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 10px;
                            padding: 20px;
                            margin-bottom: 20px;
                            transition: all 0.3s ease;
                        }}
                        .project-card:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                        }}
                        .project-header {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 15px;
                        }}
                        .priority-badge {{
                            color: white;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 0.8em;
                            font-weight: bold;
                        }}
                        .project-info {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                            gap: 10px;
                            margin-bottom: 15px;
                        }}
                        .project-description {{
                            background: white;
                            padding: 15px;
                            border-radius: 5px;
                            border-left: 3px solid #007bff;
                        }}
                        .no-projects {{
                            text-align: center;
                            padding: 60px 20px;
                        }}
                        .btn {{
                            padding: 12px 24px;
                            border-radius: 8px;
                            font-weight: 500;
                            text-decoration: none;
                            display: inline-block;
                            margin: 5px;
                        }}
                        .btn-primary {{ background: #007bff; color: white; border: none; }}
                        .btn-success {{ background: #28a745; color: white; border: none; }}
                        .btn-secondary {{ background: #6c757d; color: white; border: none; }}
                        .stats {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                            gap: 20px;
                            margin-bottom: 30px;
                        }}
                        .stat-card {{
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 20px;
                            border-radius: 10px;
                            text-align: center;
                        }}
                        .stat-number {{
                            font-size: 2em;
                            font-weight: bold;
                            display: block;
                        }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1><i class="fas fa-folder-open"></i> Mes Projets</h1>
                        </div>

                        <div class="client-info">
                            <h5><i class="fas fa-user"></i> Client connecté</h5>
                            <p><strong>Nom:</strong> {client.nom} {client.prenom if hasattr(client, 'prenom') else ''}</p>
                            <p><strong>Email:</strong> {client.email}</p>
                            <p><strong>ID Client:</strong> {client.idClient}</p>
                        </div>

                        <div class="stats">
                            <div class="stat-card">
                                <span class="stat-number">{len(projects)}</span>
                                <span>Projet(s)</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number">{sum(project.budget or 0 for project in projects):.0f}</span>
                                <span>Budget Total (DT)</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number">{sum(len(request.env['auth.projet.besoin'].sudo().search([('projet_id', '=', project.id)])) for project in projects)}</span>
                                <span>Besoin(s) Total</span>
                            </div>
                        </div>

                        <div class="projects-section">
                            {projects_html}
                        </div>

                        <div class="text-center mt-4">
                            <a href="/auth/client/create-project" class="btn btn-success">
                                <i class="fas fa-plus"></i> Créer un nouveau projet
                            </a>
                            <a href="/auth/client/login" class="btn btn-secondary">
                                <i class="fas fa-home"></i> Retour à l'accueil
                            </a>
                            <a href="/auth/client/logout" class="btn btn-secondary">
                                <i class="fas fa-sign-out-alt"></i> Se déconnecter
                            </a>
                        </div>
                    </div>
                </body>
                </html>
                """

            except Exception as model_error:
                _logger.error('Erreur lors de l\'accès au modèle projet: %s', str(model_error))
                return f"""
                <html>
                <head>
                    <title>Erreur - Mes Projets</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body {{ background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh; }}
                        .container {{ max-width: 600px; margin: 100px auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; }}
                        .btn {{ padding: 12px 24px; border-radius: 8px; font-weight: 500; text-decoration: none; display: inline-block; margin: 10px; }}
                        .btn-primary {{ background: #007bff; color: white; }}
                        .btn-success {{ background: #28a745; color: white; }}
                        h2 {{ color: #dc3545; margin-bottom: 20px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h2><i class="fas fa-exclamation-triangle"></i> Erreur Technique</h2>
                        <p>Une erreur est survenue lors du chargement de vos projets.</p>
                        <p><strong>Erreur:</strong> {str(model_error)}</p>
                        <p>Veuillez réessayer ou contacter le support technique.</p>
                        <a href="/auth/client/projects" class="btn btn-primary">
                            <i class="fas fa-redo"></i> Réessayer
                        </a>
                        <a href="/auth/client/create-project" class="btn btn-success">
                            <i class="fas fa-plus"></i> Créer un projet
                        </a>
                        <a href="/auth/client/login" class="btn btn-primary">
                            <i class="fas fa-home"></i> Retour à l'accueil
                        </a>
                    </div>
                </body>
                </html>
                """
        except Exception as e:
            _logger.error('Erreur lors de l\'affichage des projets: %s', str(e))
            return f"""
            <html>
            <head><title>Erreur - Mes Projets</title></head>
            <body style="font-family: Arial, sans-serif; margin: 20px;">
                <h1>❌ Erreur</h1>
                <p>Erreur lors du chargement des projets: {str(e)}</p>
                <p><a href="/auth/client/login">Retour à la connexion</a></p>
            </body>
            </html>
            """

    @http.route('/auth/client/create-project', type='http', auth="public", website=True, methods=['GET', 'POST'])
    def create_project(self, **kw):
        """Formulaire de création de projet"""
        if request.httprequest.method == 'POST':
            try:
                # Récupérer les données du formulaire
                project_data = {
                    'titre': kw.get('titre'),
                    'description': kw.get('description'),
                    'secteurSociete': kw.get('secteurSociete'),
                    'typeProjet': kw.get('typeProjet'),
                    'dateDebut': kw.get('dateDebut') if kw.get('dateDebut') else None,
                    'dateFin': kw.get('dateFin') if kw.get('dateFin') else None,
                    'priorite': kw.get('priorite'),
                    'budget': float(kw.get('budget', 0)) if kw.get('budget') else 0,
                }

                # Essayer de créer le projet avec le modèle auth.projet
                try:
                    # Récupérer le client connecté depuis la session
                    client_id = request.session.get('client_id')
                    if not client_id:
                        return f"""
                        <html>
                        <head><title>Erreur d'authentification</title></head>
                        <body style="font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh;">
                            <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; max-width: 500px; margin: 50px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                                <h2 style="color: #dc3545;">❌ Erreur d'authentification</h2>
                                <p>Vous devez être connecté pour créer un projet.</p>
                                <a href="/auth/client/login" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px;">Se connecter</a>
                            </div>
                        </body>
                        </html>
                        """

                    # Vérifier que le client existe toujours
                    client = request.env['auth.client'].sudo().browse(client_id)
                    if not client.exists():
                        return f"""
                        <html>
                        <head><title>Client introuvable</title></head>
                        <body style="font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh;">
                            <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; max-width: 500px; margin: 50px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                                <h2 style="color: #dc3545;">❌ Client introuvable</h2>
                                <p>Votre session a expiré. Veuillez vous reconnecter.</p>
                                <a href="/auth/client/login" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px;">Se connecter</a>
                            </div>
                        </body>
                        </html>
                        """

                    # Associer le projet au client connecté
                    project_data['client_id'] = client.id
                    project = request.env['auth.projet'].sudo().create(project_data)

                    # Traiter les besoins
                    besoins_data = []
                    i = 0
                    while f'besoin_description_{i}' in kw:
                        description = kw.get(f'besoin_description_{i}')
                        if description and description.strip():  # Seulement si description non vide
                            besoin_data = {
                                'projet_id': project.id,
                                'description': description.strip(),
                                'sequence': (i + 1) * 10,
                            }

                            # Ajouter dates si fournies
                            date_debut = kw.get(f'besoin_date_debut_{i}')
                            if date_debut:
                                besoin_data['dateDebut'] = date_debut

                            date_fin = kw.get(f'besoin_date_fin_{i}')
                            if date_fin:
                                besoin_data['dateFin'] = date_fin

                            # Ajouter budget si fourni
                            budget = kw.get(f'besoin_budget_{i}')
                            if budget:
                                try:
                                    besoin_data['budget'] = float(budget)
                                except ValueError:
                                    pass

                            besoins_data.append(besoin_data)
                        i += 1

                    # Créer les besoins
                    if besoins_data:
                        request.env['auth.projet.besoin'].sudo().create(besoins_data)

                    # Retourner une page de succès simple
                    besoins_html = ""
                    if besoins_data:
                        besoins_html = "<h4>Besoins créés:</h4><ul>"
                        for besoin in besoins_data:
                            besoins_html += f"<li>{besoin['description']}"
                            if besoin.get('budget'):
                                besoins_html += f" (Budget: {besoin['budget']} DT)"
                            besoins_html += "</li>"
                        besoins_html += "</ul>"

                    return f"""
                    <html>
                    <head>
                        <title>Projet Créé</title>
                        <meta charset="utf-8">
                        <style>
                            body {{ font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
                            .success {{ background: #d4edda; border: 1px solid #c3e6cb; padding: 30px; border-radius: 15px; text-align: center; max-width: 700px; margin: 50px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }}
                            .btn {{ padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; display: inline-block; margin: 10px; }}
                            h2 {{ color: #155724; margin-bottom: 20px; }}
                            .project-info {{ background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; }}
                            .client-info {{ background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; border-left: 4px solid #2196f3; }}
                            ul {{ text-align: left; }}
                        </style>
                    </head>
                    <body>
                        <div class="success">
                            <h2>✅ Projet PFE créé avec succès !</h2>

                            <div class="client-info">
                                <h4><i class="fas fa-user"></i> Client</h4>
                                <p><strong>Nom:</strong> {client.nom} {client.prenom if hasattr(client, 'prenom') else ''}</p>
                                <p><strong>Email:</strong> {client.email}</p>
                                <p><strong>ID Client:</strong> {client.idClient}</p>
                                {f'<p><strong>Société:</strong> {client.nomSociete}</p>' if client.nomSociete else ''}
                            </div>

                            <div class="project-info">
                                <h4><i class="fas fa-project-diagram"></i> Détails du Projet</h4>
                                <p><strong>ID Projet:</strong> {project.idProjet}</p>
                                <p><strong>Titre:</strong> {project.titre}</p>
                                <p><strong>Type:</strong> {project.typeProjet}</p>
                                <p><strong>Priorité:</strong> {project.priorite}</p>
                                <p><strong>Budget:</strong> {project.budget} DT</p>
                                {besoins_html}
                            </div>

                            <p><strong>Votre projet a été enregistré et sera traité par notre équipe commerciale.</strong></p>
                            <p>Vous recevrez une notification par email une fois qu'une offre commerciale sera disponible.</p>

                            <a href="/auth/client/login" class="btn">Retour à l'accueil</a>
                            <a href="/auth/client/projects" class="btn" style="background: #28a745;">Voir mes projets</a>
                            <a href="/auth/client/create-project" class="btn" style="background: #17a2b8;">Créer un autre projet</a>
                        </div>
                    </body>
                    </html>
                    """
                except:
                    # Si le modèle n'existe pas, afficher un message de succès simple
                    return f"""
                    <html>
                    <head>
                        <title>Projet Créé</title>
                        <meta charset="utf-8">
                        <style>
                            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }}
                            .success {{ background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; text-align: center; }}
                            .btn {{ padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; }}
                        </style>
                    </head>
                    <body>
                        <div class="success">
                            <h2>✅ Projet "{project_data['titre']}" créé avec succès !</h2>
                            <p>Votre projet a été enregistré et sera traité par notre équipe.</p>
                            <a href="/auth/client/login" class="btn">Retour à l'accueil</a>
                        </div>
                    </body>
                    </html>
                    """
            except Exception as e:
                _logger.error('Erreur lors de la création du projet: %s', str(e))
                return f"""
                <html>
                <head>
                    <title>Erreur - Création Projet</title>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }}
                        .error {{ background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; text-align: center; max-width: 600px; margin: 0 auto; }}
                        .btn {{ padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; }}
                        h2 {{ color: #721c24; }}
                    </style>
                </head>
                <body>
                    <div class="error">
                        <h2>❌ Erreur lors de la création</h2>
                        <p>Erreur: {str(e)}</p>
                        <a href="/auth/client/create-project" class="btn">Réessayer</a>
                        <a href="/auth/client/login" class="btn" style="background: #6c757d;">Retour</a>
                    </div>
                </body>
                </html>
                """

        # Vérifier si le client est connecté
        client_id = request.session.get('client_id')
        client_nom = request.session.get('client_nom', 'Client')

        if not client_id:
            return f"""
            <html>
            <head>
                <title>Connexion Requise</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body {{ background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); min-height: 100vh; }}
                    .container {{ max-width: 500px; margin: 100px auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; }}
                    .btn {{ padding: 12px 24px; border-radius: 8px; font-weight: 500; text-decoration: none; display: inline-block; margin: 10px; }}
                    .btn-primary {{ background: #007bff; color: white; }}
                    h2 {{ color: #dc3545; margin-bottom: 20px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h2><i class="fas fa-lock"></i> Connexion Requise</h2>
                    <p>Vous devez être connecté pour créer un projet.</p>
                    <p>Veuillez vous connecter avec votre compte client.</p>
                    <a href="/auth/client/login" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> Se connecter
                    </a>
                </div>
            </body>
            </html>
            """

        # Afficher le formulaire de création directement avec le nom du client
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Créer Nouveau Projet</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }}
                .container {{ max-width: 900px; margin: 30px auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }}
                .form-control, .form-select {{ margin-bottom: 15px; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; }}
                .btn {{ padding: 12px 24px; border-radius: 8px; font-weight: 500; }}
                .btn-primary {{ background: #667eea; border: none; }}
                .btn-secondary {{ background: #6c757d; border: none; }}
                .btn-success {{ background: #28a745; border: none; }}
                h2 {{ color: #333; text-align: center; margin-bottom: 30px; }}
                .needs-section {{ background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; margin: 20px 0; }}
                .need-item {{ background-color: #f8f9fa; border: 2px solid #e9ecef !important; transition: all 0.3s ease; }}
                .need-item:hover {{ border-color: #007bff !important; box-shadow: 0 4px 8px rgba(0,123,255,0.1); }}
                #add-need-btn {{ transition: all 0.2s ease; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                #add-need-btn:hover {{ transform: translateY(-1px); box-shadow: 0 4px 8px rgba(40,167,69,0.3) !important; }}
                @keyframes fadeIn {{ from {{ opacity: 0; transform: translateY(-10px); }} to {{ opacity: 1; transform: translateY(0); }} }}
            </style>
        </head>
        <body>
            <div class="container">
                <h2><i class="fas fa-plus-circle"></i> Créer Nouveau Projet</h2>
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-user"></i> <strong>Client connecté:</strong> {client_nom}
                </div>

                <form method="post" action="/auth/client/create-project">
                    <input type="hidden" name="csrf_token" value="{request.csrf_token()}">

                    <div class="row">
                        <div class="col-12">
                            <label class="form-label">Titre du projet *</label>
                            <input type="text" name="titre" class="form-control" placeholder="Entrez le titre du projet" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Type de projet *</label>
                            <select name="typeProjet" class="form-select" required>
                                <option value="">Sélectionner un type</option>
                                <option value="web">Développement Web</option>
                                <option value="mobile">Application Mobile</option>
                                <option value="erp">Système ERP</option>
                                <option value="ecommerce">E-commerce</option>
                                <option value="consulting">Conseil et Audit</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Priorité *</label>
                            <select name="priorite" class="form-select" required>
                                <option value="normale" selected>Normale</option>
                                <option value="basse">Basse</option>
                                <option value="urgente">Urgente</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <label class="form-label">Description détaillée</label>
                            <textarea name="description" class="form-control" rows="3" placeholder="Décrivez le projet en détail..."></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Date de début</label>
                            <input type="date" name="dateDebut" class="form-control">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date de fin</label>
                            <input type="date" name="dateFin" class="form-control">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <label class="form-label">Budget total (DT) *</label>
                            <input type="number" name="budget" class="form-control" placeholder="0" min="1" step="0.01" required>
                        </div>
                    </div>

                    <!-- Section Besoins -->
                    <div class="needs-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3><i class="fas fa-list-ul"></i> Besoins du projet</h3>
                            <button type="button" id="add-need-btn" class="btn btn-success btn-sm">
                                <i class="fas fa-plus-circle"></i> Ajouter une ligne
                            </button>
                        </div>
                        <p class="text-muted">Ajoutez les besoins principaux de votre projet. Les dates et budgets sont optionnels.</p>

                        <div id="needs-container">
                            <!-- 3 besoins par défaut -->
                            <div class="need-item border rounded p-3 mb-3" data-need-index="0">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number">1</span></h5>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description du besoin *</label>
                                    <textarea name="besoin_description_0" class="form-control besoin-description" rows="2" required placeholder="Décrivez ce besoin..."></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Date de début (Optionnel)</label>
                                        <input type="date" name="besoin_date_debut_0" class="form-control besoin-date-debut">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Date de fin (Optionnel)</label>
                                        <input type="date" name="besoin_date_fin_0" class="form-control besoin-date-fin">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Budget (DT) (Optionnel)</label>
                                        <input type="number" name="besoin_budget_0" class="form-control besoin-budget" placeholder="0" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>

                            <div class="need-item border rounded p-3 mb-3" data-need-index="1">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number">2</span></h5>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description du besoin *</label>
                                    <textarea name="besoin_description_1" class="form-control besoin-description" rows="2" required placeholder="Décrivez ce besoin..."></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Date de début (Optionnel)</label>
                                        <input type="date" name="besoin_date_debut_1" class="form-control besoin-date-debut">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Date de fin (Optionnel)</label>
                                        <input type="date" name="besoin_date_fin_1" class="form-control besoin-date-fin">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Budget (DT) (Optionnel)</label>
                                        <input type="number" name="besoin_budget_1" class="form-control besoin-budget" placeholder="0" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>

                            <div class="need-item border rounded p-3 mb-3" data-need-index="2">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number">3</span></h5>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description du besoin *</label>
                                    <textarea name="besoin_description_2" class="form-control besoin-description" rows="2" required placeholder="Décrivez ce besoin..."></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Date de début (Optionnel)</label>
                                        <input type="date" name="besoin_date_debut_2" class="form-control besoin-date-debut">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Date de fin (Optionnel)</label>
                                        <input type="date" name="besoin_date_fin_2" class="form-control besoin-date-fin">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Budget (DT) (Optionnel)</label>
                                        <input type="number" name="besoin_budget_2" class="form-control besoin-budget" placeholder="0" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <p class="text-muted"><em><i class="fas fa-info-circle"></i> Les descriptions sont obligatoires. Les dates et budgets sont optionnels. Seuls les besoins avec description seront enregistrés.</em></p>
                    </div>

                    <div class="text-center mt-4">
                        <a href="/auth/client/login" class="btn btn-secondary me-3">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Créer le projet
                        </button>
                    </div>
                </form>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {{
                    let needIndex = 3;
                    const needsContainer = document.getElementById('needs-container');
                    const addNeedBtn = document.getElementById('add-need-btn');

                    function addNeed() {{
                        const needHtml = `
                            <div class="need-item border rounded p-3 mb-3" data-need-index="${{needIndex}}" style="animation: fadeIn 0.3s ease-in;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number">${{needIndex + 1}}</span></h5>
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-need-btn">
                                        <i class="fas fa-trash-alt"></i> Supprimer
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description du besoin *</label>
                                    <textarea name="besoin_description_${{needIndex}}" class="form-control besoin-description" rows="2" required placeholder="Décrivez ce besoin..."></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">Date de début (Optionnel)</label>
                                        <input type="date" name="besoin_date_debut_${{needIndex}}" class="form-control besoin-date-debut">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Date de fin (Optionnel)</label>
                                        <input type="date" name="besoin_date_fin_${{needIndex}}" class="form-control besoin-date-fin">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Budget (DT) (Optionnel)</label>
                                        <input type="number" name="besoin_budget_${{needIndex}}" class="form-control besoin-budget" placeholder="0" min="0" step="0.01">
                                    </div>
                                </div>
                            </div>
                        `;

                        needsContainer.insertAdjacentHTML('beforeend', needHtml);
                        needIndex++;
                        updateNeedNumbers();

                        const newNeed = needsContainer.lastElementChild;
                        newNeed.scrollIntoView({{ behavior: 'smooth', block: 'center' }});

                        setTimeout(() => {{
                            const textarea = newNeed.querySelector('.besoin-description');
                            if (textarea) textarea.focus();
                        }}, 300);
                    }}

                    function removeNeed(needItem) {{
                        needItem.remove();
                        updateNeedNumbers();
                    }}

                    function updateNeedNumbers() {{
                        const needItems = needsContainer.querySelectorAll('.need-item');
                        needItems.forEach((item, index) => {{
                            const numberSpan = item.querySelector('.need-number');
                            if (numberSpan) numberSpan.textContent = index + 1;
                        }});
                    }}

                    addNeedBtn.addEventListener('click', addNeed);

                    needsContainer.addEventListener('click', function(e) {{
                        if (e.target.closest('.remove-need-btn')) {{
                            const needItem = e.target.closest('.need-item');
                            removeNeed(needItem);
                        }}
                    }});

                    // Validation des dates
                    const projectDateDebut = document.querySelector('input[name="dateDebut"]');
                    const projectDateFin = document.querySelector('input[name="dateFin"]');
                    const budgetTotal = document.querySelector('input[name="budget"]');

                    function validateProjectDates() {{
                        if (projectDateDebut.value && projectDateFin.value) {{
                            if (projectDateDebut.value > projectDateFin.value) {{
                                alert('La date de début ne peut pas être postérieure à la date de fin du projet.');
                                projectDateFin.value = '';
                            }}
                        }}
                    }}

                    projectDateDebut.addEventListener('change', validateProjectDates);
                    projectDateFin.addEventListener('change', validateProjectDates);

                    function validateBudgets() {{
                        const budgetInputs = needsContainer.querySelectorAll('.besoin-budget');
                        let totalBesoins = 0;

                        budgetInputs.forEach(input => {{
                            if (input.value) totalBesoins += parseFloat(input.value) || 0;
                        }});

                        const budgetTotalValue = parseFloat(budgetTotal.value) || 0;

                        if (totalBesoins > budgetTotalValue && budgetTotalValue > 0) {{
                            alert(`La somme des budgets des besoins (${{totalBesoins.toFixed(2)}} DT) ne peut pas dépasser le budget total du projet (${{budgetTotalValue.toFixed(2)}} DT).`);
                            return false;
                        }}
                        return true;
                    }}

                    document.querySelector('form').addEventListener('submit', function(e) {{
                        if (!validateBudgets()) e.preventDefault();
                    }});

                    needsContainer.addEventListener('input', function(e) {{
                        if (e.target.classList.contains('besoin-budget')) {{
                            setTimeout(validateBudgets, 100);
                        }}
                    }});
                }});
            </script>
        </body>
        </html>
        """
