# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)


class AuthProjet(models.Model):
    _name = 'auth.projet'
    _description = 'Projet Client Authentification'
    _rec_name = 'titre'
    _order = 'dateCreation desc'
    # Suppression temporaire de l'héritage mail pour éviter les problèmes de dépendances
    # _inherit = ['mail.thread', 'mail.activity.mixin']

    # Champs principaux
    idProjet = fields.Char(
        string='ID Projet',
        required=True,
        readonly=True,
        default=lambda self: self._generate_projet_id(),
        help='Identifiant unique du projet'
    )

    titre = fields.Char(
        string='Titre du projet',
        required=True,
        help='Titre descriptif du projet'
    )

    description = fields.Text(
        string='Description détaillée',
        help='Description complète du projet'
    )

    # Informations client
    client_id = fields.Many2one(
        'auth.client',
        string='Client',
        required=False,  # Changé en False pour éviter les erreurs lors de la création
        help='Client propriétaire du projet'
    )

    # Informations temporelles
    dateCreation = fields.Date(
        string='Date de création',
        default=fields.Date.today,
        required=True,
        help='Date de création du projet'
    )

    dateDebut = fields.Date(
        string='Date de début',
        help='Date prévue de début du projet'
    )

    dateFin = fields.Date(
        string='Date de fin',
        help='Date prévue de fin du projet'
    )

    duree = fields.Integer(
        string='Durée (jours)',
        help='Durée estimée du projet en jours'
    )

    # Informations financières
    budget = fields.Float(
        string='Budget total (DT)',
        required=True,
        help='Budget total alloué au projet'
    )

    budgetEstime = fields.Float(
        string='Budget estimé (DT)',
        help='Budget estimé pour le projet'
    )

    budgetFinal = fields.Float(
        string='Budget final (DT)',
        help='Budget final validé pour le projet'
    )

    # Secteur d'activité de la société
    secteurSociete = fields.Selection([
        ('industrie', 'Industrie et Manufacturing'),
        ('commerce', 'Commerce et Distribution'),
        ('services', 'Services et Consulting'),
        ('finance', 'Finance et Banque'),
        ('immobilier', 'Immobilier et Construction'),
        ('transport', 'Transport et Logistique'),
        ('technologie', 'Technologie et IT'),
        ('tourisme', 'Tourisme et Hôtellerie'),
        ('public', 'Secteur Public'),
        ('autre', 'Autre'),
    ], string='Secteur d\'activité', required=True, help='Secteur d\'activité de la société cliente')

    # Type et priorité
    typeProjet = fields.Selection([
        ('site_web_vitrine', 'Site Web Vitrine'),
        ('ecommerce_boutique', 'Boutique E-commerce'),
        ('application_mobile', 'Application Mobile'),
        ('systeme_gestion', 'Système de Gestion'),
        ('automatisation_processus', 'Automatisation de Processus'),
        ('integration_systemes', 'Intégration de Systèmes'),
        ('migration_donnees', 'Migration de Données'),
        ('formation_digitale', 'Formation Digitale'),
    ], string='Type de projet', default='site_web_vitrine', required=True)

    priorite = fields.Selection([
        ('basse', 'Basse'),
        ('normale', 'Normale'),
        ('urgente', 'Urgente')
    ], string='Priorité', default='normale', required=True)

    # Objectifs du projet
    objectifs = fields.Text(
        string='Objectifs du projet',
        help='Objectifs principaux à atteindre'
    )

    # Technologies souhaitées
    technologies = fields.Text(
        string='Technologies souhaitées',
        help='Technologies ou outils spécifiques demandés'
    )

    # Contraintes
    contraintes = fields.Text(
        string='Contraintes',
        help='Contraintes techniques, budgétaires ou temporelles'
    )

    # Relations
    besoin_ids = fields.One2many(
        'auth.projet.besoin',
        'projet_id',
        string='Besoins du projet',
        help='Liste des besoins du projet'
    )

    # Champs additionnels
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Indique si le projet est actif'
    )

    notes = fields.Text(
        string='Notes internes',
        help='Notes internes pour le suivi du projet'
    )

    # Champs de suivi
    dateModification = fields.Datetime(
        string='Dernière modification',
        default=fields.Datetime.now,
        help='Date de dernière modification'
    )

    # Champs calculés
    dureeRestante = fields.Integer(
        string='Durée restante (jours)',
        compute='_compute_duree_restante',
        help='Nombre de jours restants jusqu\'à la fin prévue'
    )

    @api.model
    def _generate_projet_id(self):
        """Génère un ID projet unique"""
        # La séquence a déjà le préfixe PRJ, pas besoin de l'ajouter
        sequence = self.env['ir.sequence'].next_by_code('auth.projet') or 'PRJ0001'
        return sequence

    @api.depends('dateFin')
    def _compute_duree_restante(self):
        """Calcule la durée restante jusqu'à la fin prévue"""
        today = fields.Date.today()
        for record in self:
            if record.dateFin:
                delta = record.dateFin - today
                record.dureeRestante = delta.days if delta.days > 0 else 0
            else:
                record.dureeRestante = 0

    @api.constrains('dateDebut', 'dateFin')
    def _check_dates(self):
        """Valide la cohérence des dates"""
        for record in self:
            if record.dateDebut and record.dateFin:
                if record.dateDebut > record.dateFin:
                    raise ValidationError(_('La date de début ne peut pas être postérieure à la date de fin.'))

    @api.constrains('budget', 'budgetEstime', 'budgetFinal')
    def _check_budgets(self):
        """Valide que les budgets sont positifs"""
        for record in self:
            if record.budget and record.budget <= 0:
                raise ValidationError(_('Le budget total doit être positif.'))
            if record.budgetEstime and record.budgetEstime < 0:
                raise ValidationError(_('Le budget estimé doit être positif.'))
            if record.budgetFinal and record.budgetFinal < 0:
                raise ValidationError(_('Le budget final doit être positif.'))

    @api.constrains('besoin_ids')
    def _check_besoins_budget(self):
        """Valide que la somme des budgets des besoins ne dépasse pas le budget total"""
        for record in self:
            if record.budget and record.besoin_ids:
                total_besoins = sum(besoin.budget for besoin in record.besoin_ids if besoin.budget)
                if total_besoins > record.budget:
                    raise ValidationError(_(
                        'La somme des budgets des besoins (%.2f DT) ne peut pas dépasser le budget total du projet (%.2f DT).'
                    ) % (total_besoins, record.budget))

    @api.model
    def create(self, vals):
        """Surcharge la création pour ajouter des logs"""
        projet = super(AuthProjet, self).create(vals)
        _logger.info(f'Nouveau projet créé: {projet.idProjet} - {projet.titre}')
        return projet

    def write(self, vals):
        """Surcharge la modification pour mettre à jour la date de modification"""
        vals['dateModification'] = fields.Datetime.now()
        result = super(AuthProjet, self).write(vals)
        return result

    def name_get(self):
        """Personnalise l'affichage du nom"""
        result = []
        for record in self:
            name = f"[{record.idProjet}] {record.titre}"
            result.append((record.id, name))
        return result

    @api.model
    def get_projets_by_client(self, client_id):
        """Récupère tous les projets d'un client spécifique"""
        return self.search([('client_id', '=', client_id), ('active', '=', True)])

    @api.model
    def get_projets_actifs(self):
        """Récupère tous les projets actifs"""
        return self.search([
            ('active', '=', True)
        ])


class AuthProjetBesoin(models.Model):
    _name = 'auth.projet.besoin'
    _description = 'Besoin de Projet'
    _rec_name = 'description'
    _order = 'sequence, id'

    # Champs principaux
    sequence = fields.Integer(string='Séquence', default=10)

    projet_id = fields.Many2one(
        'auth.projet',
        string='Projet',
        required=True,
        ondelete='cascade',
        help='Projet auquel appartient ce besoin'
    )

    description = fields.Text(
        string='Description du besoin',
        required=True,
        help='Description détaillée du besoin'
    )

    # Dates (optionnelles)
    dateDebut = fields.Date(
        string='Date de début',
        help='Date prévue de début pour ce besoin'
    )

    dateFin = fields.Date(
        string='Date de fin',
        help='Date prévue de fin pour ce besoin'
    )

    # Budget (optionnel)
    budget = fields.Float(
        string='Budget (DT)',
        help='Budget alloué à ce besoin'
    )

    # Champs additionnels
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Indique si le besoin est actif'
    )

    @api.constrains('dateDebut', 'dateFin')
    def _check_dates_besoin(self):
        """Valide la cohérence des dates du besoin"""
        for record in self:
            if record.dateDebut and record.dateFin:
                if record.dateDebut > record.dateFin:
                    raise ValidationError(_('La date de début du besoin ne peut pas être postérieure à la date de fin.'))

    @api.constrains('dateDebut', 'dateFin', 'projet_id')
    def _check_dates_in_project_range(self):
        """Valide que les dates du besoin sont dans l'intervalle du projet"""
        for record in self:
            if record.projet_id and record.projet_id.dateDebut and record.projet_id.dateFin:
                if record.dateDebut and record.dateDebut < record.projet_id.dateDebut:
                    raise ValidationError(_(
                        'La date de début du besoin ne peut pas être antérieure à la date de début du projet (%s).'
                    ) % record.projet_id.dateDebut.strftime('%d/%m/%Y'))

                if record.dateFin and record.dateFin > record.projet_id.dateFin:
                    raise ValidationError(_(
                        'La date de fin du besoin ne peut pas être postérieure à la date de fin du projet (%s).'
                    ) % record.projet_id.dateFin.strftime('%d/%m/%Y'))

    @api.constrains('budget')
    def _check_budget_positif(self):
        """Valide que le budget est positif"""
        for record in self:
            if record.budget and record.budget < 0:
                raise ValidationError(_('Le budget du besoin doit être positif.'))
