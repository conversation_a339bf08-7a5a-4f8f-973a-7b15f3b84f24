<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="create_project_form_template" name="Formulaire de Création de Projet Client">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <h2><i class="fas fa-plus-circle"></i> Créer Nouveau Projet</h2>

                        <form method="post" action="/auth/client/create-project">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">Titre du projet *</label>
                                    <input type="text" name="titre" class="form-control" placeholder="Entrez le titre du projet" required="required"/>
                                </div>
                            </div>

                            <!-- Secteur d'activité -->
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">Secteur d'activité de votre société *</label>
                                    <select name="secteurSociete" class="form-select" required="required">
                                        <option value="">Sélectionner votre secteur</option>
                                        <option value="industrie">Industrie et Manufacturing</option>
                                        <option value="commerce">Commerce et Distribution</option>
                                        <option value="services">Services et Consulting</option>
                                        <option value="sante">Santé et Médical</option>
                                        <option value="education">Éducation et Formation</option>
                                        <option value="finance">Finance et Banque</option>
                                        <option value="immobilier">Immobilier et Construction</option>
                                        <option value="transport">Transport et Logistique</option>
                                        <option value="agriculture">Agriculture et Agroalimentaire</option>
                                        <option value="technologie">Technologie et IT</option>
                                        <option value="tourisme">Tourisme et Hôtellerie</option>
                                        <option value="energie">Énergie et Environnement</option>
                                        <option value="media">Médias et Communication</option>
                                        <option value="public">Secteur Public</option>
                                        <option value="autre">Autre</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Type de projet *</label>
                                    <select name="typeProjet" class="form-select" required="required">
                                        <option value="">Sélectionner un type</option>
                                        <option value="site_web_vitrine">Site Web Vitrine</option>
                                        <option value="ecommerce_boutique">Boutique E-commerce</option>
                                        <option value="application_mobile">Application Mobile</option>
                                        <option value="systeme_gestion">Système de Gestion</option>
                                        <option value="automatisation_processus">Automatisation de Processus</option>
                                        <option value="integration_systemes">Intégration de Systèmes</option>
                                        <option value="migration_donnees">Migration de Données</option>
                                        <option value="formation_digitale">Formation Digitale</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Priorité *</label>
                                    <select name="priorite" class="form-select" required="required">
                                        <option value="normale">Normale</option>
                                        <option value="basse">Basse</option>
                                        <option value="urgente">Urgente</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">Description détaillée</label>
                                    <textarea name="description" class="form-control" rows="3" placeholder="Décrivez le projet en détail..."></textarea>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Date de début</label>
                                    <input type="date" name="dateDebut" class="form-control"/>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Date de fin</label>
                                    <input type="date" name="dateFin" class="form-control"/>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">Budget total (DT) *</label>
                                    <input type="number" name="budget" class="form-control" placeholder="0" min="1" step="0.01" required="required"/>
                                </div>
                            </div>

                            <!-- Section Besoins -->
                            <div class="needs-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h3><i class="fas fa-list-ul"></i> Besoins du projet</h3>
                                    <button type="button" id="add-need-btn" class="btn btn-success btn-sm" style="font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <i class="fas fa-plus-circle"></i> Ajouter une ligne
                                    </button>
                                </div>
                                <p class="text-muted">Ajoutez les besoins principaux de votre projet. Les dates et budgets sont optionnels.</p>

                                <div id="needs-container">
                                    <!-- 3 besoins par défaut -->
                                    <t t-foreach="range(3)" t-as="i">
                                        <div class="need-item border rounded p-3 mb-3" t-attf-data-need-index="{{i}}" style="background-color: #f8f9fa;">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number"><t t-esc="i + 1"/></span></h5>
                                                <button type="button" class="btn btn-outline-danger btn-sm remove-need-btn" t-if="i >= 3" style="display: none;">
                                                    <i class="fas fa-trash-alt"></i> Supprimer
                                                </button>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Description du besoin *</label>
                                                <textarea t-attf-name="besoin_description_{{i}}" class="form-control besoin-description" rows="2" required="required" placeholder="Décrivez ce besoin..."></textarea>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <label class="form-label">Date de début (Optionnel)</label>
                                                    <input type="date" t-attf-name="besoin_date_debut_{{i}}" class="form-control besoin-date-debut"/>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">Date de fin (Optionnel)</label>
                                                    <input type="date" t-attf-name="besoin_date_fin_{{i}}" class="form-control besoin-date-fin"/>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">Budget (DT) (Optionnel)</label>
                                                    <input type="number" t-attf-name="besoin_budget_{{i}}" class="form-control besoin-budget" placeholder="0" min="0" step="0.01"/>
                                                </div>
                                            </div>
                                        </div>
                                    </t>
                                </div>

                                <p class="text-muted"><em><i class="fas fa-info-circle"></i> Les descriptions sont obligatoires. Les dates et budgets sont optionnels. Seuls les besoins avec description seront enregistrés.</em></p>
                            </div>

                            <div class="text-center mt-4">
                                <a href="/auth/client/login" class="btn btn-secondary me-3">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Créer le projet
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- CSS pour les animations et styles -->
                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(-10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    .need-item {
                        transition: all 0.3s ease;
                        border: 2px solid #e9ecef !important;
                    }

                    .need-item:hover {
                        border-color: #007bff !important;
                        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
                    }

                    #add-need-btn {
                        transition: all 0.2s ease;
                    }

                    #add-need-btn:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(40,167,69,0.3) !important;
                    }

                    .remove-need-btn:hover {
                        transform: scale(1.05);
                    }

                    .needs-section {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        padding: 20px;
                        border-radius: 10px;
                        margin: 20px 0;
                    }
                </style>

                <!-- JavaScript pour gérer l'ajout dynamique de besoins -->
                <script>
                    <![CDATA[
                    document.addEventListener('DOMContentLoaded', function() {
                        let needIndex = 3; // Commence à 3 car on a déjà 3 besoins par défaut
                        const needsContainer = document.getElementById('needs-container');
                        const addNeedBtn = document.getElementById('add-need-btn');

                        // Fonction pour ajouter un nouveau besoin
                        function addNeed() {
                            const needHtml = `
                                <div class="need-item border rounded p-3 mb-3" data-need-index="${needIndex}" style="background-color: #f8f9fa; animation: fadeIn 0.3s ease-in;">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h5 class="mb-0 text-primary"><i class="fas fa-cog"></i> Besoin <span class="need-number">${needIndex + 1}</span></h5>
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-need-btn">
                                            <i class="fas fa-trash-alt"></i> Supprimer
                                        </button>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Description du besoin *</label>
                                        <textarea name="besoin_description_${needIndex}" class="form-control besoin-description" rows="2" required placeholder="Décrivez ce besoin..."></textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">Date de début (Optionnel)</label>
                                            <input type="date" name="besoin_date_debut_${needIndex}" class="form-control besoin-date-debut"/>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Date de fin (Optionnel)</label>
                                            <input type="date" name="besoin_date_fin_${needIndex}" class="form-control besoin-date-fin"/>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Budget (DT) (Optionnel)</label>
                                            <input type="number" name="besoin_budget_${needIndex}" class="form-control besoin-budget" placeholder="0" min="0" step="0.01"/>
                                        </div>
                                    </div>
                                </div>
                            `;

                            needsContainer.insertAdjacentHTML('beforeend', needHtml);
                            needIndex++;
                            updateNeedNumbers();

                            // Feedback visuel
                            const newNeed = needsContainer.lastElementChild;
                            newNeed.scrollIntoView({ behavior: 'smooth', block: 'center' });

                            // Focus sur la description du nouveau besoin
                            setTimeout(() => {
                                const textarea = newNeed.querySelector('.besoin-description');
                                if (textarea) {
                                    textarea.focus();
                                }
                            }, 300);
                        }

                        // Fonction pour supprimer un besoin
                        function removeNeed(needItem) {
                            needItem.remove();
                            updateNeedNumbers();
                        }

                        // Fonction pour mettre à jour la numérotation des besoins
                        function updateNeedNumbers() {
                            const needItems = needsContainer.querySelectorAll('.need-item');
                            needItems.forEach((item, index) => {
                                const numberSpan = item.querySelector('.need-number');
                                if (numberSpan) {
                                    numberSpan.textContent = index + 1;
                                }
                            });
                        }

                        // Événement pour ajouter un besoin
                        addNeedBtn.addEventListener('click', addNeed);

                        // Événement pour supprimer un besoin (délégation d'événement)
                        needsContainer.addEventListener('click', function(e) {
                            if (e.target.closest('.remove-need-btn')) {
                                const needItem = e.target.closest('.need-item');
                                removeNeed(needItem);
                            }
                        });

                        // Validation des dates du projet et des besoins
                        const projectDateDebut = document.querySelector('input[name="dateDebut"]');
                        const projectDateFin = document.querySelector('input[name="dateFin"]');
                        const budgetTotal = document.querySelector('input[name="budget"]');

                        // Validation des dates du projet
                        function validateProjectDates() {
                            if (projectDateDebut.value && projectDateFin.value) {
                                if (projectDateDebut.value > projectDateFin.value) {
                                    alert('La date de début ne peut pas être postérieure à la date de fin du projet.');
                                    projectDateFin.value = '';
                                }
                            }
                        }

                        projectDateDebut.addEventListener('change', validateProjectDates);
                        projectDateFin.addEventListener('change', validateProjectDates);

                        // Validation du budget total des besoins
                        function validateBudgets() {
                            const budgetInputs = needsContainer.querySelectorAll('.besoin-budget');
                            let totalBesoins = 0;

                            budgetInputs.forEach(input => {
                                if (input.value) {
                                    totalBesoins += parseFloat(input.value) || 0;
                                }
                            });

                            const budgetTotalValue = parseFloat(budgetTotal.value) || 0;

                            if (totalBesoins > budgetTotalValue && budgetTotalValue > 0) {
                                alert(`La somme des budgets des besoins (${totalBesoins.toFixed(2)} DT) ne peut pas dépasser le budget total du projet (${budgetTotalValue.toFixed(2)} DT).`);
                                return false;
                            }
                            return true;
                        }

                        // Validation avant soumission
                        document.querySelector('form').addEventListener('submit', function(e) {
                            if (!validateBudgets()) {
                                e.preventDefault();
                            }
                        });

                        // Validation en temps réel des budgets
                        needsContainer.addEventListener('input', function(e) {
                            if (e.target.classList.contains('besoin-budget')) {
                                setTimeout(validateBudgets, 100);
                            }
                        });
                    });
                    ]]>
                </script>
            </t>
        </template>
    </data>
</odoo>