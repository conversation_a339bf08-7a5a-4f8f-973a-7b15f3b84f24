# -*- coding: utf-8 -*-

from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class AuthCommercialController(http.Controller):

    # ===== AUTHENTIFICATION COMMERCIAUX/EXPERTS =====

    @http.route('/auth/commercial/login', type='http', auth="public", website=True, csrf=False)
    def commercial_login_form(self, **post):
        """Affiche le formulaire de connexion commercial sans navbar"""
        if request.httprequest.method == 'POST':
            return self._process_commercial_login(post)

        # Vérifier s'il y a un message de succès d'inscription
        message = request.httprequest.args.get('message')
        success_message = None
        if message == 'inscription_reussie':
            success_message = 'Inscription réussie ! Vous pouvez maintenant vous connecter.'

        # GET request - Affichage du formulaire sans navbar
        return request.render('authentification_commercial.commercial_login_standalone', {
            'values': {},
            'errors': {},
            'success_message': success_message
        })

    def _process_commercial_login(self, post):
        """Traite la connexion d'un commercial"""
        try:
            email = post.get('email')
            password = post.get('motDePasse')

            if not email or not password:
                return request.render('authentification_commercial.commercial_login_standalone', {
                    'error': 'Email et mot de passe sont obligatoires',
                    'values': post
                })

            # Authentifier le commercial
            commercial = request.env['auth.commercial'].sudo().authenticate_commercial(email, password)

            if not commercial:
                return request.render('authentification_commercial.commercial_login_standalone', {
                    'error': 'Email ou mot de passe incorrect',
                    'values': post
                })

            # Connexion réussie - rediriger vers le dashboard
            _logger.info(f'Commercial connecté avec succès: {email}')

            # Stocker l'ID du commercial dans la session pour le dashboard
            request.session['commercial_id'] = commercial.id

            return request.redirect('/auth/commercial/dashboard')

        except Exception as e:
            _logger.error('Erreur lors de la connexion commercial: %s', str(e))
            return request.render('authentification_commercial.commercial_login_standalone', {
                'error': f'Une erreur est survenue lors de la connexion: {str(e)}',
                'values': post
            })

    @http.route('/auth/commercial/register', type='http', auth="public", website=True, csrf=False)
    def commercial_register_form(self, **post):
        """Affiche le formulaire d'inscription commercial"""
        if request.httprequest.method == 'POST':
            return self._process_commercial_registration(post)

        # GET request - Affichage du formulaire
        return request.render('authentification_commercial.commercial_register_form', {
            'values': {},
            'errors': {}
        })

    def _process_commercial_registration(self, post):
        """Traite l'inscription d'un nouveau commercial"""
        try:
            # Validation des données
            errors = self._validate_commercial_registration_data(post)

            if errors:
                return request.render('authentification_commercial.commercial_register_form', {
                    'errors': errors,
                    'values': post
                })

            # Créer le commercial
            commercial_vals = {
                'nom': post.get('nom'),
                'prenom': post.get('prenom'),
                'email': post.get('email'),
                'telephone': post.get('telephone'),
                'motDePasse': post.get('motDePasse'),
            }

            # Créer le commercial dans la base de données
            commercial = request.env['auth.commercial'].sudo().create(commercial_vals)

            _logger.info(f'Commercial créé avec succès: {post.get("email")}')

            # Pour l'instant, on ne crée pas automatiquement l'utilisateur Odoo
            # L'administrateur peut le faire manuellement depuis le backend

            # Rediriger vers la connexion avec message de succès
            return request.redirect('/auth/commercial/login?message=inscription_reussie')

        except Exception as e:
            _logger.error('Erreur lors de l\'inscription commercial: %s', str(e))
            return request.render('authentification_commercial.commercial_register_form', {
                'error': f'Une erreur est survenue lors de l\'inscription: {str(e)}',
                'values': post
            })

    def _validate_commercial_registration_data(self, post):
        """Valide les données d'inscription commercial"""
        errors = {}
        import re

        # Validation du nom
        if not post.get('nom') or not post.get('nom').strip():
            errors['nom'] = 'Le nom est obligatoire'
        elif not re.match(r'^[a-zA-ZÀ-ÿ\s]+$', post.get('nom').strip()):
            errors['nom'] = 'Le nom ne doit contenir que des lettres'

        # Validation du prénom
        if not post.get('prenom') or not post.get('prenom').strip():
            errors['prenom'] = 'Le prénom est obligatoire'
        elif not re.match(r'^[a-zA-ZÀ-ÿ\s]+$', post.get('prenom').strip()):
            errors['prenom'] = 'Le prénom ne doit contenir que des lettres'

        # Validation de l'email
        if not post.get('email'):
            errors['email'] = 'L\'email est obligatoire'
        else:
            # Vérifier le format de l'email
            email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_regex, post.get('email')):
                errors['email'] = 'Format d\'email invalide'
            else:
                # Vérifier si l'email existe déjà
                existing_commercial = request.env['auth.commercial'].sudo().search([('email', '=', post.get('email'))])
                if existing_commercial:
                    errors['email'] = 'Un commercial avec cet email existe déjà'

                existing_user = request.env['res.users'].sudo().search([('login', '=', post.get('email'))])
                if existing_user:
                    errors['email'] = 'Un utilisateur avec cet email existe déjà'

        # Validation du téléphone (8 chiffres exactement)
        if not post.get('telephone'):
            errors['telephone'] = 'Le téléphone est obligatoire'
        else:
            # Nettoyer le numéro (enlever espaces, tirets, etc.)
            clean_phone = re.sub(r'[^\d]', '', post.get('telephone'))
            if len(clean_phone) != 8 or not clean_phone.isdigit():
                errors['telephone'] = 'Le numéro de téléphone doit contenir exactement 8 chiffres'

        # Validation du mot de passe
        if not post.get('motDePasse'):
            errors['motDePasse'] = 'Le mot de passe est obligatoire'
        elif len(post.get('motDePasse', '')) < 6:
            errors['motDePasse'] = 'Le mot de passe doit contenir au moins 6 caractères'

        # Validation de la confirmation du mot de passe
        if not post.get('confirmMotDePasse'):
            errors['confirmMotDePasse'] = 'La confirmation du mot de passe est obligatoire'
        elif post.get('motDePasse') != post.get('confirmMotDePasse'):
            errors['confirmMotDePasse'] = 'Les mots de passe ne correspondent pas'

        return errors

    @http.route('/auth/commercial/logout', type='http', auth="public", website=True)
    def commercial_logout(self, **kw):
        """Déconnecte le commercial"""
        # Supprimer l'ID commercial de la session
        if 'commercial_id' in request.session:
            del request.session['commercial_id']
        return request.redirect('/auth/commercial/login')

    @http.route('/auth/commercial/dashboard', type='http', auth="public", website=True)
    def commercial_dashboard(self, **kw):
        """Tableau de bord commercial avec accès à tous les clients, consultants, projets et offres"""
        # Vérifier si le commercial est connecté via la session
        commercial_id = request.session.get('commercial_id')

        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        # Récupérer le commercial
        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)

        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer tous les clients
        try:
            clients = request.env['auth.client'].sudo().search([])
        except:
            clients = []

        # Récupérer tous les consultants
        try:
            consultants = request.env['auth.consultant'].sudo().search([])
        except:
            consultants = []

        # Récupérer tous les projets clients
        try:
            projets = request.env['auth.projet'].sudo().search([('active', '=', True)])
        except:
            projets = []

        # Récupérer toutes les offres commerciales
        try:
            offres = request.env['commercial.offre'].sudo().search([])
        except:
            offres = []

        # Statistiques complètes
        stats = {
            'total_clients': len(clients),
            'clients_actifs': len([c for c in clients if c.active]) if clients else 0,
            'total_consultants': len(consultants),
            'consultants_actifs': len([c for c in consultants if c.active]) if consultants else 0,
            'total_projets': len(projets),
            'projets_actifs': len([p for p in projets if p.active]) if projets else 0,
            'total_offres': len(offres),
            'offres_en_attente': len([o for o in offres if o.state == 'draft']) if offres else 0,
        }

        return request.render('authentification_commercial.commercial_dashboard', {
            'commercial': commercial,
            'clients': clients,
            'consultants': consultants,
            'projets': projets,
            'offres': offres,
            'stats': stats
        })

    @http.route('/auth/commercial/projets', type='http', auth="public", website=True)
    def commercial_projets(self, **kw):
        """Liste de tous les projets pour les commerciaux"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer tous les projets clients
        try:
            projets = request.env['auth.projet'].sudo().search([('active', '=', True)])
        except:
            projets = []

        return request.render('authentification_commercial.commercial_projets_list', {
            'commercial': commercial,
            'projets': projets
        })

    @http.route('/auth/commercial/projet/<int:projet_id>', type='http', auth="public", website=True)
    def commercial_projet_detail(self, projet_id, **kw):
        """Vue détaillée d'un projet avec possibilité de créer une offre"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer le projet client
        try:
            projet = request.env['auth.projet'].sudo().browse(projet_id)
            if not projet.exists():
                return request.redirect('/auth/commercial/projets')
        except:
            return request.redirect('/auth/commercial/projets')

        # Récupérer les offres existantes pour ce projet
        try:
            offres_existantes = request.env['commercial.offre'].sudo().search([
                ('project_id', '=', projet_id)
            ])
        except:
            offres_existantes = []

        return request.render('authentification_commercial.commercial_projet_detail', {
            'commercial': commercial,
            'projet': projet,
            'offres_existantes': offres_existantes
        })

    @http.route('/auth/commercial/offres', type='http', auth="public", website=True)
    def commercial_offres(self, **kw):
        """Liste de toutes les offres commerciales"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer toutes les offres
        try:
            offres = request.env['commercial.offre'].sudo().search([])
        except:
            offres = []

        return request.render('authentification_commercial.commercial_offres_list', {
            'commercial': commercial,
            'offres': offres
        })

    @http.route('/auth/commercial/creer-offre/<int:projet_id>', type='http', auth="public", website=True, methods=['GET', 'POST'])
    def commercial_creer_offre(self, projet_id, **post):
        """Créer une offre commerciale basée sur un projet"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer le projet client
        try:
            projet = request.env['auth.projet'].sudo().browse(projet_id)
            if not projet.exists():
                return request.redirect('/auth/commercial/projets')
        except:
            return request.redirect('/auth/commercial/projets')

        # Récupérer les services commerciaux disponibles
        try:
            services = request.env['service.commercial'].sudo().search([
                ('active', '=', True)
            ], order='categorie, sequence, name')
        except:
            services = []

        if post:
            try:
                # Validation des contraintes budgétaires
                budget_final = float(post.get('budgetFinal', 0.0)) if post.get('budgetFinal') else 0.0
                if budget_final > projet.budget:
                    error_message = f"Le budget final ({budget_final} TND) ne peut pas dépasser le budget du projet ({projet.budget} TND)"
                    return request.render('authentification_commercial.commercial_creer_offre', {
                        'commercial': commercial,
                        'projet': projet,
                        'services': services,
                        'error': error_message,
                        'values': post
                    })

                # Créer l'offre commerciale
                offre_vals = {
                    'project_id': projet.id,
                    'commercial_id': commercial.id,
                    'description': post.get('description', ''),
                    'typeOffre': post.get('typeOffre', 'standard'),
                    'duree': int(post.get('duree', 0)) if post.get('duree') else 0,
                    'budgetFinal': budget_final,
                }

                offre = request.env['commercial.offre'].sudo().create(offre_vals)

                # Créer les lignes de service (gestion dynamique)
                service_count = 0
                i = 1
                while True:
                    service_key = f'service_{i}'
                    prix_key = f'prix_{i}'
                    duree_key = f'duree_service_{i}'

                    # Si aucun service n'est trouvé pour cet index, arrêter la boucle
                    if service_key not in post:
                        break

                    if post.get(service_key) and post.get(prix_key):
                        line_vals = {
                            'offre_id': offre.id,
                            'product_id': int(post.get(service_key)),
                            'price_unit': float(post.get(prix_key)),
                            'quantity': float(post.get(duree_key, 1.0)) if post.get(duree_key) else 1.0,
                        }
                        request.env['commercial.offre.line'].sudo().create(line_vals)
                        service_count += 1

                    i += 1

                # Vérifier qu'au moins un service a été ajouté
                if service_count == 0:
                    # Supprimer l'offre créée si aucun service
                    offre.sudo().unlink()
                    error_message = "Veuillez sélectionner au moins un service pour créer l'offre"
                    return request.render('authentification_commercial.commercial_creer_offre', {
                        'commercial': commercial,
                        'projet': projet,
                        'services': services,
                        'error': error_message,
                        'values': post
                    })

                success_message = f"Offre {offre.idOffre} créée avec succès pour le projet '{projet.name}'"
                return request.render('authentification_commercial.commercial_creer_offre', {
                    'commercial': commercial,
                    'projet': projet,
                    'services': services,
                    'success': success_message,
                    'offre': offre
                })

            except Exception as e:
                error_message = f"Erreur lors de la création de l'offre: {str(e)}"
                return request.render('authentification_commercial.commercial_creer_offre', {
                    'commercial': commercial,
                    'projet': projet,
                    'services': services,
                    'error': error_message,
                    'values': post
                })

        return request.render('authentification_commercial.commercial_creer_offre', {
            'commercial': commercial,
            'projet': projet,
            'services': services
        })

    # ROUTE DE TEST - À SUPPRIMER EN PRODUCTION
    @http.route('/auth/commercial/test/projet/<int:projet_id>', type='http', auth="public", website=True)
    def test_commercial_projet_detail(self, projet_id, **kw):
        """Route de test pour voir un projet sans authentification"""
        # Créer un commercial de test automatiquement
        commercial = request.env['auth.commercial'].sudo().search([('email', '=', '<EMAIL>')], limit=1)
        if not commercial:
            commercial = request.env['auth.commercial'].sudo().create({
                'nom': 'Test',
                'prenom': 'Commercial',
                'email': '<EMAIL>',
                'telephone': '12345678',
                'motDePasse': 'test123',
                'active': True
            })

        # Récupérer le projet client
        try:
            projet = request.env['auth.projet'].sudo().browse(projet_id)
            if not projet.exists():
                return request.render('authentification_commercial.commercial_layout', {
                    'error': f'Projet avec ID {projet_id} non trouvé'
                })
        except Exception as e:
            return request.render('authentification_commercial.commercial_layout', {
                'error': f'Erreur lors de la récupération du projet: {str(e)}'
            })

        # Récupérer les offres existantes pour ce projet
        try:
            offres_existantes = request.env['commercial.offre'].sudo().search([
                ('project_id', '=', projet_id)
            ])
        except:
            offres_existantes = []

        return request.render('authentification_commercial.commercial_projet_detail', {
            'commercial': commercial,
            'projet': projet,
            'offres_existantes': offres_existantes
        })

    @http.route('/auth/commercial/clients', type='http', auth="public", website=True)
    def commercial_clients(self, **kw):
        """Liste de tous les clients pour les commerciaux"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer tous les clients
        try:
            clients = request.env['auth.client'].sudo().search([])
        except:
            clients = []

        return request.render('authentification_commercial.commercial_clients_list', {
            'commercial': commercial,
            'clients': clients
        })

    @http.route('/auth/commercial/consultants', type='http', auth="public", website=True)
    def commercial_consultants(self, **kw):
        """Liste de tous les consultants pour les commerciaux"""
        # Vérifier si le commercial est connecté
        commercial_id = request.session.get('commercial_id')
        if not commercial_id:
            return request.redirect('/auth/commercial/login')

        commercial = request.env['auth.commercial'].sudo().browse(commercial_id)
        if not commercial.exists():
            return request.redirect('/auth/commercial/login')

        # Récupérer tous les consultants
        try:
            consultants = request.env['auth.consultant'].sudo().search([])
        except:
            consultants = []

        return request.render('authentification_commercial.commercial_consultants_list', {
            'commercial': commercial,
            'consultants': consultants
        })

    # ROUTES DE TEST - À SUPPRIMER EN PRODUCTION
    @http.route('/auth/commercial/test/dashboard', type='http', auth="public", website=True)
    def test_commercial_dashboard(self, **kw):
        """Route de test pour le tableau de bord sans authentification"""
        # Créer un commercial de test automatiquement
        commercial = request.env['auth.commercial'].sudo().search([('email', '=', '<EMAIL>')], limit=1)
        if not commercial:
            commercial = request.env['auth.commercial'].sudo().create({
                'nom': 'Test',
                'prenom': 'Commercial',
                'email': '<EMAIL>',
                'telephone': '12345678',
                'motDePasse': 'test123',
                'active': True
            })

        # Récupérer tous les clients
        try:
            clients = request.env['auth.client'].sudo().search([])
        except:
            clients = []

        # Récupérer tous les consultants
        try:
            consultants = request.env['auth.consultant'].sudo().search([])
        except:
            consultants = []

        # Récupérer tous les projets clients
        try:
            projets = request.env['auth.projet'].sudo().search([('active', '=', True)])
        except:
            projets = []

        # Récupérer toutes les offres commerciales
        try:
            offres = request.env['commercial.offre'].sudo().search([])
        except:
            offres = []

        # Calculer les statistiques
        stats = {
            'total_clients': len(clients),
            'clients_actifs': len([c for c in clients if c.active]) if clients else 0,
            'total_consultants': len(consultants),
            'total_projets': len(projets),
            'projets_actifs': len([p for p in projets if p.active]) if projets else 0,
            'total_offres': len(offres),
            'offres_en_attente': len([o for o in offres if o.state == 'draft']) if offres else 0,
        }

        return request.render('authentification_commercial.commercial_dashboard', {
            'commercial': commercial,
            'clients': clients,
            'consultants': consultants,
            'projets': projets,
            'offres': offres,
            'stats': stats
        })

    @http.route('/auth/commercial/test/clients', type='http', auth="public", website=True)
    def test_commercial_clients(self, **kw):
        """Route de test pour la liste des clients sans authentification"""
        # Créer un commercial de test automatiquement
        commercial = request.env['auth.commercial'].sudo().search([('email', '=', '<EMAIL>')], limit=1)
        if not commercial:
            commercial = request.env['auth.commercial'].sudo().create({
                'nom': 'Test',
                'prenom': 'Commercial',
                'email': '<EMAIL>',
                'telephone': '12345678',
                'motDePasse': 'test123',
                'active': True
            })

        # Récupérer tous les clients
        try:
            clients = request.env['auth.client'].sudo().search([])
        except:
            clients = []

        return request.render('authentification_commercial.commercial_clients_list', {
            'commercial': commercial,
            'clients': clients
        })

    @http.route('/auth/commercial/test/consultants', type='http', auth="public", website=True)
    def test_commercial_consultants(self, **kw):
        """Route de test pour la liste des consultants sans authentification"""
        # Créer un commercial de test automatiquement
        commercial = request.env['auth.commercial'].sudo().search([('email', '=', '<EMAIL>')], limit=1)
        if not commercial:
            commercial = request.env['auth.commercial'].sudo().create({
                'nom': 'Test',
                'prenom': 'Commercial',
                'email': '<EMAIL>',
                'telephone': '12345678',
                'motDePasse': 'test123',
                'active': True
            })

        # Récupérer tous les consultants
        try:
            consultants = request.env['auth.consultant'].sudo().search([])
        except:
            consultants = []

        return request.render('authentification_commercial.commercial_consultants_list', {
            'commercial': commercial,
            'consultants': consultants
        })

    # ===== SÉLECTION DE SERVICES =====

    @http.route('/auth/commercial/services', type='http', auth="public", website=True)
    def commercial_services_selection(self, **kw):
        """Page de sélection de services en français"""
        # Récupérer tous les services actifs
        services = request.env['service.commercial'].sudo().search([
            ('active', '=', True)
        ], order='categorie, sequence, name')

        return request.render('authentification_commercial.service_selection_page', {
            'services': services
        })

    @http.route('/auth/commercial/services/select', type='http', auth="public", website=True, csrf=False, methods=['POST'])
    def process_service_selection(self, **post):
        """Traiter la sélection de services"""
        try:
            selected_services = []
            total_amount = 0

            # Parcourir les données POST pour trouver les services sélectionnés
            for key, value in post.items():
                if key.startswith('service_') and value:
                    service_id = int(value)
                    quantity_key = f'quantity_{service_id}'
                    quantity = float(post.get(quantity_key, 1))

                    # Récupérer le service
                    service = request.env['service.commercial'].sudo().browse(service_id)
                    if service.exists():
                        subtotal = quantity * service.prix_unitaire
                        selected_services.append({
                            'service': service,
                            'quantity': quantity,
                            'subtotal': subtotal
                        })
                        total_amount += subtotal

            if not selected_services:
                return request.render('authentification_commercial.service_selection_page', {
                    'services': request.env['service.commercial'].sudo().search([('active', '=', True)]),
                    'error': 'Veuillez sélectionner au moins un service.'
                })

            # Afficher le résumé de la sélection
            return request.render('authentification_commercial.service_selection_summary', {
                'selected_services': selected_services,
                'total_amount': total_amount
            })

        except Exception as e:
            _logger.error(f'Erreur lors de la sélection de services: {str(e)}')
            return request.render('authentification_commercial.service_selection_page', {
                'services': request.env['service.commercial'].sudo().search([('active', '=', True)]),
                'error': 'Une erreur est survenue lors du traitement de votre sélection.'
            })

