<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Services de Conseil -->
        <record id="service_conseil_strategique" model="service.commercial">
            <field name="name">Conseil Stratégique</field>
            <field name="description">Accompagnement stratégique pour le développement de votre entreprise</field>
            <field name="categorie">conseil</field>
            <field name="prix_unitaire">150.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="service_conseil_digital" model="service.commercial">
            <field name="name">Conseil en Transformation Digitale</field>
            <field name="description">Accompagnement dans la digitalisation de vos processus métier</field>
            <field name="categorie">conseil</field>
            <field name="prix_unitaire">120.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="service_conseil_commercial" model="service.commercial">
            <field name="name">Conseil Commercial</field>
            <field name="description">Optimisation de vos processus commerciaux et stratégies de vente</field>
            <field name="categorie">conseil</field>
            <field name="prix_unitaire">100.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <!-- Services de Formation -->
        <record id="service_formation_odoo" model="service.commercial">
            <field name="name">Formation Odoo</field>
            <field name="description">Formation complète sur l'utilisation et la configuration d'Odoo</field>
            <field name="categorie">formation</field>
            <field name="prix_unitaire">800.0</field>
            <field name="unite_mesure">jour</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <record id="service_formation_gestion" model="service.commercial">
            <field name="name">Formation Gestion de Projet</field>
            <field name="description">Formation aux méthodologies de gestion de projet (Agile, Scrum, etc.)</field>
            <field name="categorie">formation</field>
            <field name="prix_unitaire">600.0</field>
            <field name="unite_mesure">jour</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">50</field>
            <field name="active">True</field>
        </record>

        <record id="service_formation_digital" model="service.commercial">
            <field name="name">Formation Outils Digitaux</field>
            <field name="description">Formation sur les outils digitaux pour améliorer la productivité</field>
            <field name="categorie">formation</field>
            <field name="prix_unitaire">500.0</field>
            <field name="unite_mesure">jour</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">60</field>
            <field name="active">True</field>
        </record>

        <!-- Services de Développement -->
        <record id="service_dev_web" model="service.commercial">
            <field name="name">Développement Web</field>
            <field name="description">Développement d'applications web sur mesure</field>
            <field name="categorie">developpement</field>
            <field name="prix_unitaire">80.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">70</field>
            <field name="active">True</field>
        </record>

        <record id="service_dev_mobile" model="service.commercial">
            <field name="name">Développement Mobile</field>
            <field name="description">Développement d'applications mobiles iOS et Android</field>
            <field name="categorie">developpement</field>
            <field name="prix_unitaire">90.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">80</field>
            <field name="active">True</field>
        </record>

        <record id="service_dev_odoo" model="service.commercial">
            <field name="name">Développement Module Odoo</field>
            <field name="description">Développement de modules personnalisés pour Odoo</field>
            <field name="categorie">developpement</field>
            <field name="prix_unitaire">100.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">90</field>
            <field name="active">True</field>
        </record>

        <!-- Services de Maintenance -->
        <record id="service_maintenance_serveur" model="service.commercial">
            <field name="name">Maintenance Serveur</field>
            <field name="description">Maintenance et surveillance de vos serveurs</field>
            <field name="categorie">maintenance</field>
            <field name="prix_unitaire">1200.0</field>
            <field name="unite_mesure">mois</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">100</field>
            <field name="active">True</field>
        </record>

        <record id="service_maintenance_odoo" model="service.commercial">
            <field name="name">Maintenance Odoo</field>
            <field name="description">Maintenance et mise à jour de votre instance Odoo</field>
            <field name="categorie">maintenance</field>
            <field name="prix_unitaire">800.0</field>
            <field name="unite_mesure">mois</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">110</field>
            <field name="active">True</field>
        </record>

        <!-- Services d'Audit -->
        <record id="service_audit_securite" model="service.commercial">
            <field name="name">Audit de Sécurité</field>
            <field name="description">Audit complet de la sécurité de votre système d'information</field>
            <field name="categorie">audit</field>
            <field name="prix_unitaire">2500.0</field>
            <field name="unite_mesure">forfait</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">120</field>
            <field name="active">True</field>
        </record>

        <record id="service_audit_processus" model="service.commercial">
            <field name="name">Audit des Processus</field>
            <field name="description">Analyse et optimisation de vos processus métier</field>
            <field name="categorie">audit</field>
            <field name="prix_unitaire">3000.0</field>
            <field name="unite_mesure">forfait</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">130</field>
            <field name="active">True</field>
        </record>

        <!-- Services de Support -->
        <record id="service_support_technique" model="service.commercial">
            <field name="name">Support Technique</field>
            <field name="description">Support technique pour résoudre vos problèmes informatiques</field>
            <field name="categorie">support</field>
            <field name="prix_unitaire">60.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">140</field>
            <field name="active">True</field>
        </record>

        <record id="service_support_utilisateur" model="service.commercial">
            <field name="name">Support Utilisateur</field>
            <field name="description">Assistance aux utilisateurs pour l'utilisation des logiciels</field>
            <field name="categorie">support</field>
            <field name="prix_unitaire">45.0</field>
            <field name="unite_mesure">heure</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="sequence">150</field>
            <field name="active">True</field>
        </record>

        <!-- Services supplémentaires -->

        <!-- Service 16 : Analyse de Données -->
        <record id="service_analyse_donnees" model="service.commercial">
            <field name="name">Analyse de Données</field>
            <field name="description">Analyse et traitement de données métier</field>
            <field name="prix_unitaire">95.0</field>
            <field name="unite_mesure">heure</field>
            <field name="categorie">conseil</field>
            <field name="sequence">160</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="active">True</field>
        </record>

        <!-- Service 17 : Intégration API -->
        <record id="service_integration_api" model="service.commercial">
            <field name="name">Intégration API</field>
            <field name="description">Développement et intégration d'APIs</field>
            <field name="prix_unitaire">110.0</field>
            <field name="unite_mesure">heure</field>
            <field name="categorie">developpement</field>
            <field name="sequence">170</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="active">True</field>
        </record>

        <!-- Service 18 : Formation Personnalisée -->
        <record id="service_formation_personnalisee" model="service.commercial">
            <field name="name">Formation Personnalisée</field>
            <field name="description">Formation sur mesure selon vos besoins</field>
            <field name="prix_unitaire">750.0</field>
            <field name="unite_mesure">jour</field>
            <field name="categorie">formation</field>
            <field name="sequence">180</field>
            <field name="date_debut" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="active">True</field>
        </record>

        <!-- Service 19 : Optimisation Performance -->
        <record id="service_optimisation_performance" model="service.commercial">
            <field name="name">Optimisation Performance</field>
            <field name="description">Optimisation des performances système</field>
            <field name="prix_unitaire">130.0</field>
            <field name="unite_mesure">heure</field>
            <field name="categorie">maintenance</field>
            <field name="sequence">190</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="active">True</field>
        </record>

        <!-- Service 20 : Conseil en Transformation Digitale -->
        <record id="service_transformation_digitale" model="service.commercial">
            <field name="name">Conseil en Transformation Digitale</field>
            <field name="description">Accompagnement dans la transformation digitale</field>
            <field name="prix_unitaire">180.0</field>
            <field name="unite_mesure">heure</field>
            <field name="categorie">conseil</field>
            <field name="sequence">200</field>
            <field name="date_debut" eval="(DateTime.now()).strftime('%Y-%m-%d')"/>
            <field name="active">True</field>
        </record>
    </data>
</odoo>
