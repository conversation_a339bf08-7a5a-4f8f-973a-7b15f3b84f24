# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class CommercialOffre(models.Model):
    _name = 'commercial.offre'
    _description = 'Offre Commerciale'
    _order = 'dateCreation desc'

    # Champs principaux
    idOffre = fields.Char(
        string='ID Offre',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: self.env['ir.sequence'].next_by_code('commercial.offre') or 'OFF-000'
    )
    
    project_id = fields.Many2one(
        'auth.projet',
        string='Projet',
        required=True,
        help='Projet associé à cette offre'
    )
    
    client_id = fields.Many2one(
        'auth.client',
        string='Client',
        related='project_id.client_id',
        store=True,
        readonly=True
    )
    
    description = fields.Text(
        string='Description',
        required=True,
        help='Description détaillée de l\'offre'
    )
    
    typeOffre = fields.Selection([
        ('standard', 'Standard'),
        ('premium', 'Premium'),
        ('personnalisee', 'Personnalisée')
    ], string='Type d\'offre', required=True, default='standard')
    
    budgetFinal = fields.Float(
        string='Budget Final (TND)',
        required=True,
        help='Budget final de l\'offre'
    )
    
    duree = fields.Integer(
        string='Durée (jours)',
        required=True,
        help='Durée estimée en jours'
    )
    
    dateCreation = fields.Datetime(
        string='Date de création',
        default=fields.Datetime.now,
        readonly=True
    )
    
    dateExpiration = fields.Date(
        string='Date d\'expiration',
        compute='_compute_date_expiration',
        store=True
    )
    
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('sent', 'Envoyée'),
        ('accepted', 'Acceptée'),
        ('rejected', 'Refusée'),
        ('expired', 'Expirée')
    ], string='État', default='draft')
    
    # Champs de relation
    commercial_id = fields.Many2one(
        'auth.commercial',
        string='Commercial',
        required=True,
        help='Commercial qui a créé l\'offre'
    )
    
    consultant_id = fields.Many2one(
        'auth.consultant',
        string='Consultant validateur',
        help='Consultant qui valide l\'offre'
    )
    
    # Lignes de service
    line_ids = fields.One2many(
        'commercial.offre.line',
        'offre_id',
        string='Lignes de service'
    )
    
    # Champs calculés
    total_amount = fields.Float(
        string='Montant total',
        compute='_compute_total_amount',
        store=True
    )
    
    @api.depends('dateCreation')
    def _compute_date_expiration(self):
        for record in self:
            if record.dateCreation:
                record.dateExpiration = record.dateCreation.date() + timedelta(days=30)
            else:
                record.dateExpiration = False
    
    @api.depends('line_ids.subtotal')
    def _compute_total_amount(self):
        for record in self:
            record.total_amount = sum(line.subtotal for line in record.line_ids)

    @api.constrains('budgetFinal', 'project_id')
    def _check_budget_constraint(self):
        """Vérifier que le budget final ne dépasse pas le budget du projet"""
        for record in self:
            if record.project_id and record.budgetFinal > record.project_id.budget:
                raise ValidationError(
                    f"Le budget final ({record.budgetFinal} TND) ne peut pas dépasser "
                    f"le budget du projet ({record.project_id.budget} TND)"
                )
    
    def action_send(self):
        """Envoyer l'offre au client"""
        self.state = 'sent'
        return True
    
    def action_accept(self):
        """Accepter l'offre"""
        self.state = 'accepted'
        return True
    
    def action_reject(self):
        """Rejeter l'offre"""
        self.state = 'rejected'
        return True


class CommercialOffreLine(models.Model):
    _name = 'commercial.offre.line'
    _description = 'Ligne d\'offre commerciale'

    offre_id = fields.Many2one(
        'commercial.offre',
        string='Offre',
        required=True,
        ondelete='cascade'
    )

    # Utiliser directement le service commercial
    service_id = fields.Many2one(
        'service.commercial',
        string='Service',
        required=True
    )

    # Garder product_id pour compatibilité mais le rendre optionnel
    product_id = fields.Many2one(
        'product.product',
        string='Produit Odoo',
        related='service_id.product_id',
        store=True,
        readonly=True
    )

    name = fields.Char(
        string='Description',
        related='service_id.name',
        store=True
    )
    
    quantity = fields.Float(
        string='Quantité',
        default=1.0,
        required=True
    )
    
    price_unit = fields.Float(
        string='Prix unitaire (TND)',
        required=True
    )
    
    subtotal = fields.Float(
        string='Sous-total',
        compute='_compute_subtotal',
        store=True
    )
    
    @api.depends('quantity', 'price_unit')
    def _compute_subtotal(self):
        for line in self:
            line.subtotal = line.quantity * line.price_unit
