# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ServiceCommercial(models.Model):
    _name = 'service.commercial'
    _description = 'Service Commercial'
    _order = 'sequence, name'

    # Champs principaux
    name = fields.Char(
        string='Nom du Service',
        required=True,
        help='Nom du service proposé'
    )

    description = fields.Text(
        string='Description',
        help='Description détaillée du service'
    )

    sequence = fields.Integer(
        string='Séquence',
        default=10,
        help='Ordre d\'affichage des services'
    )

    # Champs de prix et dates
    prix_unitaire = fields.Float(
        string='Prix Unitaire (TND)',
        required=True,
        help='Prix unitaire du service en dinars tunisiens'
    )

    date_debut = fields.Date(
        string='Date de Début',
        default=fields.Date.today,
        help='Date de début de disponibilité du service'
    )

    date_fin = fields.Date(
        string='Date de Fin',
        help='Date de fin de disponibilité du service (optionnel)'
    )

    # Champs de gestion
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Si décoché, le service ne sera plus disponible'
    )

    unite_mesure = fields.Selection([
        ('heure', 'Heure'),
        ('jour', 'Jour'),
        ('semaine', 'Semaine'),
        ('mois', 'Mois'),
        ('unite', 'Unité'),
        ('forfait', 'Forfait')
    ], string='Unité de Mesure', default='heure', required=True)

    # Champs de catégorie
    categorie = fields.Selection([
        ('conseil', 'Conseil'),
        ('formation', 'Formation'),
        ('maintenance', 'Maintenance'),
        ('developpement', 'Développement'),
        ('audit', 'Audit'),
        ('support', 'Support Technique')
    ], string='Catégorie', required=True, default='conseil')

    # Relation avec le produit Odoo
    product_id = fields.Many2one(
        'product.product',
        string='Produit Odoo',
        help='Produit Odoo correspondant à ce service'
    )

    # Contraintes et validations
    @api.constrains('prix_unitaire')
    def _check_prix_unitaire(self):
        """Vérifier que le prix unitaire est positif"""
        for record in self:
            if record.prix_unitaire <= 0:
                raise ValidationError('Le prix unitaire doit être supérieur à zéro.')

    @api.constrains('date_debut', 'date_fin')
    def _check_dates(self):
        """Vérifier que la date de fin est après la date de début"""
        for record in self:
            if record.date_fin and record.date_debut and record.date_fin < record.date_debut:
                raise ValidationError('La date de fin doit être postérieure à la date de début.')

    @api.model
    def create(self, vals):
        """Créer automatiquement un produit Odoo lors de la création d'un service"""
        service = super(ServiceCommercial, self).create(vals)
        service._create_product()
        return service

    def write(self, vals):
        """Mettre à jour le produit Odoo lors de la modification d'un service"""
        result = super(ServiceCommercial, self).write(vals)
        for service in self:
            if service.product_id:
                service._update_product()
            else:
                service._create_product()
        return result

    def _create_product(self):
        """Créer un produit Odoo correspondant à ce service"""
        if not self.product_id:
            # Créer la catégorie de produit si elle n'existe pas
            category = self._get_or_create_category()

            product_vals = {
                'name': self.name,
                'type': 'service',
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': self.prix_unitaire,
                'description_sale': self.description,
                'categ_id': category.id,
                'uom_id': self._get_uom_id(),
                'uom_po_id': self._get_uom_id(),
                'active': self.active,
            }

            product = self.env['product.product'].sudo().create(product_vals)
            self.product_id = product.id
            _logger.info(f'Produit créé pour le service {self.name}: {product.id}')

    def _update_product(self):
        """Mettre à jour le produit Odoo correspondant"""
        if self.product_id:
            category = self._get_or_create_category()

            product_vals = {
                'name': self.name,
                'list_price': self.prix_unitaire,
                'description_sale': self.description,
                'categ_id': category.id,
                'uom_id': self._get_uom_id(),
                'uom_po_id': self._get_uom_id(),
                'active': self.active,
            }

            self.product_id.sudo().write(product_vals)
            _logger.info(f'Produit mis à jour pour le service {self.name}: {self.product_id.id}')

    def _get_or_create_category(self):
        """Obtenir ou créer la catégorie de produit selon le type de service"""
        category_name = f'Services - {self.categorie.title()}'
        category = self.env['product.category'].sudo().search([('name', '=', category_name)], limit=1)

        if not category:
            category = self.env['product.category'].sudo().create({
                'name': category_name,
                'parent_id': False,
            })
            _logger.info(f'Catégorie créée: {category_name}')

        return category

    def _get_uom_id(self):
        """Obtenir l'unité de mesure Odoo correspondante"""
        uom_mapping = {
            'heure': 'product.product_uom_hour',
            'jour': 'product.product_uom_day',
            'semaine': 'product.product_uom_week',
            'mois': 'product.product_uom_month',
            'unite': 'uom.product_uom_unit',
            'forfait': 'uom.product_uom_unit',
        }

        uom_ref = uom_mapping.get(self.unite_mesure, 'uom.product_uom_unit')
        try:
            uom = self.env.ref(uom_ref)
            return uom.id
        except:
            # Fallback vers l'unité par défaut
            return self.env.ref('uom.product_uom_unit').id

    @api.model
    def sync_all_services_to_products(self):
        """Synchroniser tous les services existants avec des produits Odoo"""
        services_without_products = self.search([('product_id', '=', False)])

        _logger.info(f'Synchronisation de {len(services_without_products)} services sans produits')

        for service in services_without_products:
            try:
                service._create_product()
            except Exception as e:
                _logger.error(f'Erreur lors de la création du produit pour le service {service.name}: {str(e)}')

        _logger.info('Synchronisation terminée')
        return True
