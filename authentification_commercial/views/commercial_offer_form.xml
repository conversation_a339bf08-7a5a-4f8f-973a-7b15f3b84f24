<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Template pour le formulaire de creation d'offre -->
        <template id="commercial_creer_offre" name="Creer Offre - Commercial">
            <t t-call="authentification_commercial.commercial_layout">
                <div class="container-fluid mt-4">
                    <!-- En-tete -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <div class="card-body">
                                    <h2 class="mb-1">
                                        <i class="fas fa-plus-circle me-2"/>
                                        Creer une Offre Commerciale
                                    </h2>
                                    <p class="mb-0">
                                        Projet : <strong t-esc="projet.titre"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Messages d'erreur et de succes -->
                    <t t-if="error">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-danger">
                                    <t t-esc="error"/>
                                </div>
                            </div>
                        </div>
                    </t>
                    <t t-if="success">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"/>
                                    <t t-esc="success"/>
                                    <br/>
                                    <small class="text-muted">Votre offre a ete creee avec succes et est maintenant disponible dans la liste des offres.</small>
                                </div>
                            </div>
                        </div>
                    </t>

                    <div class="row">
                        <!-- Formulaire principal -->
                        <div class="col-md-8">
                            <form method="post" id="offreForm">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                
                                <!-- Informations generales -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0">Informations generales</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="description" class="form-label">Description *</label>
                                                <textarea class="form-control" id="description" name="description" rows="3" required="true"></textarea>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="typeOffre" class="form-label">Type d'offre *</label>
                                                <select class="form-select" id="typeOffre" name="typeOffre" required="true">
                                                    <option value="">Selectionnez un type</option>
                                                    <option value="standard">Standard</option>
                                                    <option value="premium">Premium</option>
                                                    <option value="personnalisee">Personnalisee</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label for="budgetFinal" class="form-label">Budget final (TND) *</label>
                                                <input type="number" class="form-control" id="budgetFinal" name="budgetFinal"
                                                       step="0.01" min="0" t-att-max="projet.budget" required="true"
                                                       onchange="validateBudget()"/>
                                                <div class="form-text">
                                                    Budget maximum autorisé : <strong t-esc="projet.budget"/> TND
                                                </div>
                                                <div id="budgetError" class="text-danger" style="display: none;">
                                                    Le budget final ne peut pas dépasser le budget du projet !
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Durée calculée automatiquement -->
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label">Durée du projet</label>
                                                <div class="card bg-light">
                                                    <div class="card-body py-2">
                                                        <div class="row">
                                                            <div class="col-md-4">
                                                                <small class="text-muted">Date début :</small><br/>
                                                                <strong t-esc="projet.dateDebut or 'Non définie'"/>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <small class="text-muted">Date fin :</small><br/>
                                                                <strong t-esc="projet.dateFin or 'Non définie'"/>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <small class="text-muted">Durée totale :</small><br/>
                                                                <strong><span id="dureeProjets" t-esc="projet.duree or '0'"/> jours</strong>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <input type="hidden" id="duree" name="duree" t-att-value="projet.duree"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Services -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Services proposes</h5>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="addServiceBtn">
                                            <i class="fas fa-plus me-1"></i>
                                            Ajouter un service
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div id="servicesContainer">
                                            <!-- Service 1 -->
                                            <div class="service-line row mb-3 p-3 border rounded" data-service-index="1">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="text-primary mb-0">Service 1</h6>
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-service-btn" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="col-md-4 mb-2">
                                                    <label for="service_1" class="form-label">Service</label>
                                                    <select class="form-select service-select" id="service_1" name="service_1" onchange="updateServiceInfo(1)">
                                                        <option value="">Selectionnez un service</option>
                                                        <t t-foreach="services" t-as="service">
                                                            <option t-att-value="service.id"
                                                                    t-att-data-price="service.prix_unitaire"
                                                                    t-att-data-unit="service.unite_mesure"
                                                                    t-att-data-description="service.description"
                                                                    t-att-data-date-debut="service.date_debut"
                                                                    t-att-data-date-fin="service.date_fin">
                                                                <t t-esc="service.name"/> - <t t-esc="service.prix_unitaire"/> TND/<t t-esc="service.unite_mesure"/>
                                                            </option>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <label for="duree_service_1" class="form-label">Quantite</label>
                                                    <input type="number" class="form-control quantity-input" id="duree_service_1" name="duree_service_1"
                                                           step="0.01" min="0" value="1" onchange="calculateSubtotal(1)"/>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <label class="form-label">Prix unitaire (TND)</label>
                                                    <input type="text" class="form-control price-display" id="prix_display_1" readonly="readonly" value="0.00"/>
                                                    <input type="hidden" id="prix_1" name="prix_1" value="0"/>
                                                </div>
                                                <div class="col-md-2 mb-2">
                                                    <label class="form-label">Sous-total (TND)</label>
                                                    <input type="text" class="form-control subtotal-display" id="subtotal_1" readonly="readonly" value="0.00"/>
                                                </div>
                                                <!-- Informations du service -->
                                                <div class="col-12 mt-2">
                                                    <div id="service_info_1" class="alert alert-info" style="display: none;">
                                                        <small>
                                                            <strong>Description:</strong> <span id="service_desc_1"></span><br/>
                                                            <strong>Période:</strong> <span id="service_period_1"></span><br/>
                                                            <strong>Unité:</strong> <span id="service_unit_1"></span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Service 2 -->
                                            <div class="service-line row mb-3 p-3 border rounded" data-service-index="2">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="text-primary mb-0">Service 2</h6>
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-service-btn" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="col-md-4 mb-2">
                                                    <label for="service_2" class="form-label">Service</label>
                                                    <select class="form-select service-select" id="service_2" name="service_2" onchange="updateServiceInfo(2)">
                                                        <option value="">Selectionnez un service</option>
                                                        <t t-foreach="services" t-as="service">
                                                            <option t-att-value="service.id"
                                                                    t-att-data-price="service.prix_unitaire"
                                                                    t-att-data-unit="service.unite_mesure"
                                                                    t-att-data-description="service.description"
                                                                    t-att-data-date-debut="service.date_debut"
                                                                    t-att-data-date-fin="service.date_fin">
                                                                <t t-esc="service.name"/> - <t t-esc="service.prix_unitaire"/> TND/<t t-esc="service.unite_mesure"/>
                                                            </option>
                                                        </t>
                                                    </select>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <label for="duree_service_2" class="form-label">Quantite</label>
                                                    <input type="number" class="form-control quantity-input" id="duree_service_2" name="duree_service_2"
                                                           step="0.01" min="0" value="1" onchange="calculateSubtotal(2)"/>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <label class="form-label">Prix unitaire (TND)</label>
                                                    <input type="text" class="form-control price-display" id="prix_display_2" readonly="readonly" value="0.00"/>
                                                    <input type="hidden" id="prix_2" name="prix_2" value="0"/>
                                                </div>
                                                <div class="col-md-2 mb-2">
                                                    <label class="form-label">Sous-total (TND)</label>
                                                    <input type="text" class="form-control subtotal-display" id="subtotal_2" readonly="readonly" value="0.00"/>
                                                </div>
                                                <!-- Informations du service -->
                                                <div class="col-12 mt-2">
                                                    <div id="service_info_2" class="alert alert-info" style="display: none;">
                                                        <small>
                                                            <strong>Description:</strong> <span id="service_desc_2"></span><br/>
                                                            <strong>Période:</strong> <span id="service_period_2"></span><br/>
                                                            <strong>Unité:</strong> <span id="service_unit_2"></span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>

                                        <!-- Total général -->
                                        <div class="row mt-3">
                                            <div class="col-md-8"></div>
                                            <div class="col-md-4">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between">
                                                            <strong>Total des services :</strong>
                                                            <strong id="totalServices">0.00 TND</strong>
                                                        </div>
                                                        <div class="d-flex justify-content-between mt-2">
                                                            <span>Budget projet :</span>
                                                            <span id="budgetProjet" t-esc="projet.budget">0.00</span> TND
                                                        </div>
                                                        <div class="d-flex justify-content-between mt-2">
                                                            <span>Budget restant :</span>
                                                            <span id="budgetRestant" class="text-success">0.00 TND</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons -->
                                <div class="card">
                                    <div class="card-body text-center">
                                        <button type="submit" class="btn btn-success btn-lg me-3" id="submitBtn">
                                            <i class="fas fa-save me-2"/>
                                            Creer l'offre
                                        </button>
                                        <a t-att-href="'/auth/commercial/projet/' + str(projet.id)" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times me-2"/>
                                            Annuler
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Sidebar -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Informations du projet</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>Nom :</strong></td>
                                            <td t-esc="projet.titre"/>
                                        </tr>
                                        <tr>
                                            <td><strong>Client :</strong></td>
                                            <td t-esc="projet.client_id.nom if projet.client_id else 'Non defini'"/>
                                        </tr>
                                        <tr>
                                            <td><strong>Budget initial :</strong></td>
                                            <td><strong t-esc="projet.budget"/> TND</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Date debut :</strong></td>
                                            <td t-esc="projet.dateDebut or 'Non definie'"/>
                                        </tr>
                                        <tr>
                                            <td><strong>Date fin :</strong></td>
                                            <td t-esc="projet.dateFin or 'Non definie'"/>
                                        </tr>
                                        <tr>
                                            <td><strong>Duree prevue :</strong></td>
                                            <td><t t-esc="projet.duree or '0'"/> jours</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Type :</strong></td>
                                            <td>
                                                <span class="badge bg-secondary" t-esc="projet.typeProjet"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Besoins du projet -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Besoins du projet</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Description des besoins :</strong>
                                        <p class="mt-2 text-muted" t-esc="projet.description or 'Aucune description fournie'"/>
                                    </div>

                                    <t t-if="projet.besoin_ids">
                                        <div class="mb-3">
                                            <strong>Besoins spécifiques :</strong>
                                            <ul class="list-unstyled mt-2">
                                                <t t-foreach="projet.besoin_ids" t-as="besoin">
                                                    <li class="mb-2">
                                                        <i class="fas fa-check-circle text-success me-1"></i>
                                                        <span t-esc="besoin.description"/>
                                                        <t t-if="besoin.budget">
                                                            <small class="text-muted"> (Budget: <span t-esc="besoin.budget"/> TND)</small>
                                                        </t>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </t>

                                    <t t-if="projet.technologies">
                                        <div class="mb-3">
                                            <strong>Technologies souhaitées :</strong>
                                            <p class="mt-2 text-muted" t-esc="projet.technologies"/>
                                        </div>
                                    </t>

                                    <t t-if="projet.objectifs">
                                        <div class="mb-3">
                                            <strong>Objectifs :</strong>
                                            <p class="mt-2 text-muted" t-esc="projet.objectifs"/>
                                        </div>
                                    </t>

                                    <t t-if="projet.contraintes">
                                        <div class="mb-3">
                                            <strong>Contraintes :</strong>
                                            <p class="mt-2 text-muted" t-esc="projet.contraintes"/>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- JavaScript pour les fonctionnalités dynamiques -->
                <script type="text/javascript">
                    <![CDATA[
                    let serviceCounter = 3; // Commence à 4 car nous avons déjà 3 services
                    const budgetProjet = parseFloat('<t t-esc="projet.budget"/>') || 0;

                    // Stocker les données des services pour les services dynamiques
                    const services = [
                        <t t-foreach="services" t-as="service">
                            {
                                id: <t t-esc="service.id"/>,
                                name: '<t t-esc="service.name"/>',
                                price: <t t-esc="service.prix_unitaire"/>,
                                unit: '<t t-esc="service.unite_mesure"/>',
                                description: '<t t-esc="service.description"/>',
                                date_debut: '<t t-esc="service.date_debut"/>',
                                date_fin: '<t t-esc="service.date_fin"/>'
                            }<t t-if="not service_last">,</t>
                        </t>
                    ];

                    // Fonction pour ajouter un nouveau service
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialiser les calculs
                        calculateTotalServices();
                        updateBudgetDisplay();

                        // Bouton ajouter service
                        document.getElementById('addServiceBtn').addEventListener('click', function() {
                            console.log('Bouton ajouter service cliqué');
                            addNewService();
                        });

                        // Gestion des boutons de suppression
                        document.addEventListener('click', function(e) {
                            if (e.target.classList.contains('remove-service-btn') || e.target.parentElement.classList.contains('remove-service-btn')) {
                                const serviceDiv = e.target.closest('.service-line');
                                removeService(serviceDiv);
                            }
                        });

                        // Validation du formulaire
                        document.getElementById('offreForm').addEventListener('submit', function(e) {
                            if (!validateForm()) {
                                e.preventDefault();
                            }
                        });

                        // Afficher les boutons de suppression si plus de 3 services
                        updateRemoveButtons();
                    });

                    function addNewService() {
                        serviceCounter++;
                        console.log('Ajout du service numéro:', serviceCounter);
                        const container = document.getElementById('servicesContainer');

                        // Générer les options des services
                        let optionsHtml = '<option value="">Selectionnez un service</option>';
                        services.forEach(function(service) {
                            optionsHtml += `<option value="${service.id}"
                                                   data-price="${service.price}"
                                                   data-unit="${service.unit}"
                                                   data-description="${service.description}"
                                                   data-date-debut="${service.date_debut}"
                                                   data-date-fin="${service.date_fin}">
                                               ${service.name} - ${service.price} TND/${service.unit}
                                           </option>`;
                        });

                        const serviceHtml = `
                            <div class="service-line row mb-3 p-3 border rounded" data-service-index="${serviceCounter}">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="text-primary mb-0">Service ${serviceCounter}</h6>
                                    <button type="button" class="btn btn-outline-danger btn-sm remove-service-btn">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label for="service_${serviceCounter}" class="form-label">Service</label>
                                    <select class="form-select service-select" id="service_${serviceCounter}" name="service_${serviceCounter}" onchange="updateServiceInfo(${serviceCounter})">
                                        ${optionsHtml}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label for="duree_service_${serviceCounter}" class="form-label">Quantite</label>
                                    <input type="number" class="form-control quantity-input" id="duree_service_${serviceCounter}" name="duree_service_${serviceCounter}"
                                           step="0.01" min="0" value="1" onchange="calculateSubtotal(${serviceCounter})"/>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <label class="form-label">Prix unitaire (TND)</label>
                                    <input type="text" class="form-control price-display" id="prix_display_${serviceCounter}" readonly="readonly" value="0.00"/>
                                    <input type="hidden" id="prix_${serviceCounter}" name="prix_${serviceCounter}" value="0"/>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <label class="form-label">Sous-total (TND)</label>
                                    <input type="text" class="form-control subtotal-display" id="subtotal_${serviceCounter}" readonly="readonly" value="0.00"/>
                                </div>
                                <!-- Informations du service -->
                                <div class="col-12 mt-2">
                                    <div id="service_info_${serviceCounter}" class="alert alert-info" style="display: none;">
                                        <small>
                                            <strong>Description:</strong> <span id="service_desc_${serviceCounter}"></span><br/>
                                            <strong>Période:</strong> <span id="service_period_${serviceCounter}"></span><br/>
                                            <strong>Unité:</strong> <span id="service_unit_${serviceCounter}"></span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        `;

                        container.insertAdjacentHTML('beforeend', serviceHtml);
                        updateRemoveButtons();
                    }

                    function removeService(serviceDiv) {
                        serviceDiv.remove();
                        calculateTotalServices();
                        updateBudgetDisplay();
                        updateRemoveButtons();
                    }

                    function updateRemoveButtons() {
                        const serviceLines = document.querySelectorAll('.service-line');
                        serviceLines.forEach((line, index) => {
                            const removeBtn = line.querySelector('.remove-service-btn');
                            if (serviceLines.length > 3) {
                                removeBtn.style.display = 'inline-block';
                            } else {
                                removeBtn.style.display = 'none';
                            }
                        });
                    }

                    function updateServiceInfo(index) {
                        const select = document.getElementById(`service_${index}`);
                        const priceInput = document.getElementById(`prix_${index}`);
                        const priceDisplay = document.getElementById(`prix_display_${index}`);
                        const infoDiv = document.getElementById(`service_info_${index}`);
                        const descSpan = document.getElementById(`service_desc_${index}`);
                        const periodSpan = document.getElementById(`service_period_${index}`);
                        const unitSpan = document.getElementById(`service_unit_${index}`);

                        if (select.selectedIndex > 0) {
                            const selectedOption = select.options[select.selectedIndex];
                            const price = selectedOption.getAttribute('data-price') || 0;
                            const unit = selectedOption.getAttribute('data-unit') || '';
                            const description = selectedOption.getAttribute('data-description') || '';
                            const dateDebut = selectedOption.getAttribute('data-date-debut') || '';
                            const dateFin = selectedOption.getAttribute('data-date-fin') || '';

                            // Mettre à jour le prix (caché et affiché)
                            if (priceInput) priceInput.value = parseFloat(price).toFixed(2);
                            if (priceDisplay) priceDisplay.value = parseFloat(price).toFixed(2) + ' TND';

                            // Afficher les informations du service
                            if (descSpan) descSpan.textContent = description;
                            if (unitSpan) unitSpan.textContent = unit;
                            if (periodSpan) {
                                let period = `Du ${dateDebut}`;
                                if (dateFin && dateFin !== 'False') {
                                    period += ` au ${dateFin}`;
                                } else {
                                    period += ' (durée indéterminée)';
                                }
                                periodSpan.textContent = period;
                            }

                            if (infoDiv) infoDiv.style.display = 'block';
                            calculateSubtotal(index);
                        } else {
                            if (infoDiv) infoDiv.style.display = 'none';
                            if (priceInput) priceInput.value = '0';
                            if (priceDisplay) priceDisplay.value = '0.00';
                            calculateSubtotal(index);
                        }
                    }

                    function calculateSubtotal(index) {
                        const price = parseFloat(document.getElementById(`prix_${index}`).value) || 0;
                        const quantity = parseFloat(document.getElementById(`duree_service_${index}`).value) || 0;
                        const subtotal = price * quantity;

                        document.getElementById(`subtotal_${index}`).value = subtotal.toFixed(2);
                        calculateTotalServices();
                        updateBudgetDisplay();
                    }

                    function calculateTotalServices() {
                        let total = 0;
                        document.querySelectorAll('.subtotal-display').forEach(function(input) {
                            total += parseFloat(input.value) || 0;
                        });

                        document.getElementById('totalServices').textContent = total.toFixed(2) + ' TND';
                        return total;
                    }

                    function updateBudgetDisplay() {
                        const totalServices = calculateTotalServices();
                        const budgetRestant = budgetProjet - totalServices;
                        const budgetRestantElement = document.getElementById('budgetRestant');

                        budgetRestantElement.textContent = budgetRestant.toFixed(2) + ' TND';

                        if (budgetRestant < 0) {
                            budgetRestantElement.className = 'text-danger';
                        } else {
                            budgetRestantElement.className = 'text-success';
                        }
                    }

                    function validateBudget() {
                        const budgetFinal = parseFloat(document.getElementById('budgetFinal').value) || 0;
                        const budgetError = document.getElementById('budgetError');
                        const submitBtn = document.getElementById('submitBtn');

                        if (budgetFinal > budgetProjet) {
                            budgetError.style.display = 'block';
                            submitBtn.disabled = true;
                            return false;
                        } else {
                            budgetError.style.display = 'none';
                            submitBtn.disabled = false;
                            return true;
                        }
                    }

                    function validateForm() {
                        // Validation du budget
                        if (!validateBudget()) {
                            alert('Le budget final ne peut pas dépasser le budget du projet !');
                            return false;
                        }

                        // Vérifier qu'au moins un service est sélectionné
                        let hasService = false;
                        document.querySelectorAll('.service-select').forEach(function(select) {
                            if (select.value) {
                                hasService = true;
                            }
                        });

                        if (!hasService) {
                            alert('Veuillez sélectionner au moins un service !');
                            return false;
                        }

                        return true;
                    }
                    ]]>
                </script>
            </t>
        </template>
    </data>
</odoo>
