#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour synchroniser tous les services commerciaux avec des produits Odoo
"""

import sys
import os

# Ajouter le chemin d'Odoo au PYTHONPATH
odoo_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
sys.path.insert(0, odoo_path)

import odoo
from odoo import api, SUPERUSER_ID

def sync_services_to_products(db_name):
    """Synchroniser tous les services commerciaux avec des produits Odoo"""
    
    # Initialiser Odoo
    odoo.tools.config.parse_config(['-d', db_name])
    
    with odoo.registry(db_name).cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        try:
            # Récupérer tous les services commerciaux
            services = env['service.commercial'].search([])
            
            print(f"Trouvé {len(services)} services commerciaux")
            
            services_without_products = services.filtered(lambda s: not s.product_id)
            print(f"{len(services_without_products)} services sans produits associés")
            
            # Synchroniser chaque service
            for service in services_without_products:
                try:
                    print(f"Création du produit pour le service: {service.name}")
                    service._create_product()
                    print(f"  -> Produit créé avec l'ID: {service.product_id.id}")
                except Exception as e:
                    print(f"  -> Erreur: {str(e)}")
            
            # Valider les changements
            cr.commit()
            print("Synchronisation terminée avec succès!")
            
        except Exception as e:
            print(f"Erreur lors de la synchronisation: {str(e)}")
            cr.rollback()

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python sync_services.py <database_name>")
        print("Exemple: python sync_services.py test2")
        sys.exit(1)
    
    db_name = sys.argv[1]
    sync_services_to_products(db_name)
