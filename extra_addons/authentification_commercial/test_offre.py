#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour créer une offre et vérifier que l'erreur est résolue
"""

import requests
import json

def test_create_offre():
    """Test de création d'offre"""
    
    # URL de base
    base_url = "http://localhost:8092"
    
    # Session pour maintenir les cookies
    session = requests.Session()
    
    try:
        # 1. Créer un commercial de test
        print("1. Création d'un commercial de test...")
        register_data = {
            'nom': 'Test',
            'prenom': 'Offre',
            'email': '<EMAIL>',
            'telephone': '12345678',
            'motDePasse': 'test123',
            'confirmMotDePasse': 'test123'
        }
        
        response = session.post(f"{base_url}/auth/commercial/register", data=register_data)
        print(f"Inscription: {response.status_code}")
        
        # 2. Se connecter
        print("2. Connexion...")
        login_data = {
            'email': '<EMAIL>',
            'motDePasse': 'test123'
        }
        
        response = session.post(f"{base_url}/auth/commercial/login", data=login_data)
        print(f"Connexion: {response.status_code}")
        
        # 3. Accéder à la page de création d'offre
        print("3. Accès à la page de création d'offre...")
        response = session.get(f"{base_url}/auth/commercial/creer-offre/12")
        print(f"Page création offre: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Page de création d'offre accessible!")
            
            # 4. Essayer de créer une offre
            print("4. Création d'une offre de test...")
            offre_data = {
                'description': 'Offre de test pour vérifier la correction du bug',
                'typeOffre': 'standard',
                'duree': '30',
                'budgetFinal': '2000.0',
                'service_1': '1',  # ID du premier service
                'prix_1': '150.0',
                'duree_service_1': '10'
            }
            
            response = session.post(f"{base_url}/auth/commercial/creer-offre/12", data=offre_data)
            print(f"Création offre: {response.status_code}")
            
            if response.status_code == 200:
                if "créée avec succès" in response.text:
                    print("✅ Offre créée avec succès!")
                elif "Erreur" in response.text or "erreur" in response.text:
                    print("❌ Erreur lors de la création de l'offre")
                    # Extraire le message d'erreur
                    if "Erreur" in response.text:
                        start = response.text.find("Erreur")
                        end = response.text.find("</", start)
                        if end > start:
                            error_msg = response.text[start:end]
                            print(f"Message d'erreur: {error_msg}")
                else:
                    print("⚠️ Statut de création incertain")
            else:
                print(f"❌ Erreur HTTP lors de la création: {response.status_code}")
                
        else:
            print(f"❌ Impossible d'accéder à la page de création: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

if __name__ == '__main__':
    test_create_offre()
