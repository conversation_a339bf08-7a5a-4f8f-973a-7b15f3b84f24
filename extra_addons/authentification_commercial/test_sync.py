#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour synchroniser les services avec les produits
"""

import requests
import json

def test_sync():
    """Test de la synchronisation via l'interface web"""
    
    # URL de base
    base_url = "http://localhost:8092"
    
    # Session pour maintenir les cookies
    session = requests.Session()
    
    try:
        # 1. Créer un commercial de test
        print("1. Création d'un commercial de test...")
        register_data = {
            'nom': 'Test',
            'prenom': 'Commercial',
            'email': '<EMAIL>',
            'telephone': '12345678',
            'motDePasse': 'test123',
            'confirmMotDePasse': 'test123'
        }
        
        response = session.post(f"{base_url}/auth/commercial/register", data=register_data)
        print(f"Inscription: {response.status_code}")
        
        # 2. Se connecter
        print("2. Connexion...")
        login_data = {
            'email': '<EMAIL>',
            'motDePasse': 'test123'
        }
        
        response = session.post(f"{base_url}/auth/commercial/login", data=login_data)
        print(f"Connexion: {response.status_code}")
        
        # 3. Accéder à la synchronisation
        print("3. Synchronisation des services...")
        response = session.get(f"{base_url}/auth/commercial/sync-services")
        print(f"Synchronisation: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Synchronisation réussie!")
        else:
            print(f"❌ Erreur lors de la synchronisation: {response.status_code}")
            print(response.text[:500])
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

if __name__ == '__main__':
    test_sync()
